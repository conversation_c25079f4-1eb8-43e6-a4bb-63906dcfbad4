/**
 * @license
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * @fileoverview Externs for MDL methods.
 * @externs
 */


/** @const */
const componentHandler = {};

/** @const */
componentHandler.upgradeDom = function() {};


/** @constructor */
const MaterialLayout = function() {};

MaterialLayout.prototype.toggleDrawer = function() {};

/** @const */
MaterialLayout.prototype.Constant_ = {};

/** @type {string} */
MaterialLayout.prototype.Constant_.MENU_ICON;

/** @const {?MaterialLayout} */
Element.prototype.MaterialLayout;
