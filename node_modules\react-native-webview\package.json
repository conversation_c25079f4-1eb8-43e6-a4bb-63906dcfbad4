{"name": "react-native-webview", "description": "React Native WebView component for iOS, Android, macOS, and Windows", "main": "index.js", "main-internal": "src/index.ts", "react-native": "src/index.ts", "typings": "index.d.ts", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "version": "13.15.0", "homepage": "https://github.com/react-native-webview/react-native-webview#readme", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "macos": "react-native run-macos --scheme WebviewExample --project-path example/macos", "start": "react-native start", "windows": "install-windows-test-app --project-directory example/windows && react-native run-windows --root example --arch x64", "ci": "CI=true && yarn lint", "ci:publish": "yarn semantic-release", "lint": "yarn tsc --noEmit && yarn eslint ./src --ext .ts,.tsx,.js,.jsx", "build": "babel --extensions \".ts,.tsx\" --out-dir lib src", "prepare:types": "tsc --noEmit false --emitDeclarationOnly --declaration --rootDir src --outDir lib", "prepare": "yarn prepare:types && yarn build", "appium": "appium", "test:windows": "yarn jest --setupFiles=./jest-setups/jest.setup.js", "add:macos": "yarn add react-native-macos@0.73.17"}, "rn-docs": {"title": "Webview", "type": "Component"}, "peerDependencies": {"react": "*", "react-native": "*"}, "dependencies": {"escape-string-regexp": "^4.0.0", "invariant": "2.2.4"}, "devDependencies": {"@babel/cli": "^7.20.0", "@babel/core": "^7.20.0", "@babel/runtime": "^7.20.0", "@callstack/react-native-visionos": "0.73.8", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@rnx-kit/metro-config": "1.3.15", "@semantic-release/git": "7.0.16", "@types/invariant": "^2.2.30", "@types/jest": "^29.5.12", "@types/react": "18.2.61", "@types/selenium-webdriver": "4.0.9", "appium": "1.17.0", "eslint": "8.57.0", "jest": "^29.6.3", "prettier": "2.8.8", "react": "18.2.0", "react-native": "0.73.5", "react-native-macos": "0.73.17", "react-native-test-app": "3.7.2", "react-native-windows": "0.73.8", "selenium-appium": "1.0.2", "selenium-webdriver": "4.0.0-alpha.7", "semantic-release": "15.13.24", "typescript": "5.1.3", "winappdriver": "^0.0.7"}, "repository": {"type": "git", "url": "https://github.com/react-native-webview/react-native-webview.git"}, "files": ["android/src", "android/build.gradle", "android/gradle.properties", "apple", "ios", "macos", "windows", "lib", "src", "index.js", "index.d.ts", "react-native-webview.podspec", "react-native.config.js"], "codegenConfig": {"name": "RNCWebViewSpec", "type": "all", "jsSrcsDir": "./src", "android": {"javaPackageName": "com.reactnativecommunity.webview"}, "ios": {"componentProvider": {"RNCWebView": "RNCWebView"}, "modulesProvider": {"RNCWebViewModule": "RNCWebViewModule"}}}, "packageManager": "yarn@1.22.19"}