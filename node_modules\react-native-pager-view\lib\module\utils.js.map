{"version": 3, "names": ["React", "Children", "StyleSheet", "View", "childrenWithOverriddenStyle", "children", "map", "child", "element", "createElement", "style", "absoluteFill", "collapsable", "cloneElement", "props"], "sources": ["utils.tsx"], "sourcesContent": ["import React, { Children, ReactNode } from 'react';\nimport { StyleSheet, View } from 'react-native';\n\nexport const childrenWithOverriddenStyle = (children?: ReactNode) => {\n  return Children.map(children, (child) => {\n    const element = child as React.ReactElement<any>;\n    return (\n      // Add a wrapper to ensure layout is calculated correctly\n      <View style={StyleSheet.absoluteFill} collapsable={false}>\n        {React.cloneElement(element, {\n          ...element.props,\n          // Override styles so that each page will fill the parent.\n          style: [element.props.style, StyleSheet.absoluteFill],\n        })}\n      </View>\n    );\n  });\n};\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAE/C,OAAO,MAAMC,2BAA2B,GAAIC,QAAoB,IAAK;EACnE,OAAOJ,QAAQ,CAACK,GAAG,CAACD,QAAQ,EAAGE,KAAK,IAAK;IACvC,MAAMC,OAAO,GAAGD,KAAgC;IAChD;MAAA;MACE;MACAP,KAAA,CAAAS,aAAA,CAACN,IAAI;QAACO,KAAK,EAAER,UAAU,CAACS,YAAa;QAACC,WAAW,EAAE;MAAM,gBACtDZ,KAAK,CAACa,YAAY,CAACL,OAAO,EAAE;QAC3B,GAAGA,OAAO,CAACM,KAAK;QAChB;QACAJ,KAAK,EAAE,CAACF,OAAO,CAACM,KAAK,CAACJ,KAAK,EAAER,UAAU,CAACS,YAAY;MACtD,CAAC,CACG;IAAC;EAEX,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}