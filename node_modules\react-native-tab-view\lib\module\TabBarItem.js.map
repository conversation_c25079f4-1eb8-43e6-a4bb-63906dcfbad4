{"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "View", "useLatestCallback", "PlatformPressable", "TabBarItemLabel", "jsx", "_jsx", "jsxs", "_jsxs", "DEFAULT_ACTIVE_COLOR", "DEFAULT_INACTIVE_COLOR", "ICON_SIZE", "getActiveOpacity", "position", "routesLength", "tabIndex", "inputRange", "Array", "from", "length", "_", "i", "interpolate", "outputRange", "map", "getInactiveOpacity", "ANDROID_RIPPLE_DEFAULT", "borderless", "TabBarItemInternal", "accessibilityLabel", "accessible", "label", "customlabel", "testID", "onLongPress", "onPress", "isFocused", "style", "inactiveColor", "inactiveColorCustom", "activeColor", "activeColorCustom", "labelStyle", "onLayout", "index", "pressColor", "pressOpacity", "defaultTabWidth", "icon", "customIcon", "badge", "customBadge", "href", "labelText", "android_ripple", "labelAllowFontScaling", "route", "labelColorFromStyle", "flatten", "color", "undefined", "activeOpacity", "inactiveOpacity", "useMemo", "inactiveIcon", "focused", "size", "activeIcon", "styles", "children", "opacity", "absoluteFill", "renderLabel", "useCallback", "allowFontScaling", "tabStyle", "isWidthSet", "width", "tabContainerStyle", "aria<PERSON><PERSON><PERSON>", "role", "unstable_pressDelay", "pressable", "pointerEvents", "item", "MemoizedTabBarItemInternal", "memo", "TabBarItem", "props", "navigationState", "rest", "onPressLatest", "onLongPressLatest", "onLayoutLatest", "routes", "indexOf", "create", "margin", "flex", "alignItems", "justifyContent", "padding", "minHeight", "top", "end", "backgroundColor", "select", "ios", "borderRadius", "default"], "sourceRoot": "../../src", "sources": ["TabBarItem.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAERC,QAAQ,EAGRC,UAAU,EACVC,IAAI,QAEC,cAAc;AACrB,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,SAASC,iBAAiB,QAAQ,wBAAqB;AACvD,SAASC,eAAe,QAAQ,sBAAmB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAmBpD,MAAMC,oBAAoB,GAAG,wBAAwB;AACrD,MAAMC,sBAAsB,GAAG,0BAA0B;AACzD,MAAMC,SAAS,GAAG,EAAE;AAEpB,MAAMC,gBAAgB,GAAGA,CACvBC,QAAgD,EAChDC,YAAoB,EACpBC,QAAgB,KACb;EACH,IAAID,YAAY,GAAG,CAAC,EAAE;IACpB,MAAME,UAAU,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAEL;IAAa,CAAC,EAAE,CAACM,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;IAEpE,OAAOR,QAAQ,CAACS,WAAW,CAAC;MAC1BN,UAAU;MACVO,WAAW,EAAEP,UAAU,CAACQ,GAAG,CAAEH,CAAC,IAAMA,CAAC,KAAKN,QAAQ,GAAG,CAAC,GAAG,CAAE;IAC7D,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,CAAC;EACV;AACF,CAAC;AAED,MAAMU,kBAAkB,GAAGA,CACzBZ,QAAgD,EAChDC,YAAoB,EACpBC,QAAgB,KACb;EACH,IAAID,YAAY,GAAG,CAAC,EAAE;IACpB,MAAME,UAAU,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAEL;IAAa,CAAC,EAAE,CAACM,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;IAEpE,OAAOR,QAAQ,CAACS,WAAW,CAAC;MAC1BN,UAAU;MACVO,WAAW,EAAEP,UAAU,CAACQ,GAAG,CAAEH,CAAS,IAAMA,CAAC,KAAKN,QAAQ,GAAG,CAAC,GAAG,CAAE;IACrE,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,OAAO,CAAC;EACV;AACF,CAAC;AAgBD,MAAMW,sBAAsB,GAAG;EAAEC,UAAU,EAAE;AAAK,CAAC;AAEnD,MAAMC,kBAAkB,GAAGA,CAAkB;EAC3CC,kBAAkB;EAClBC,UAAU;EACVC,KAAK,EAAEC,WAAW;EAClBC,MAAM;EACNC,WAAW;EACXC,OAAO;EACPC,SAAS;EACTvB,QAAQ;EACRwB,KAAK;EACLC,aAAa,EAAEC,mBAAmB;EAClCC,WAAW,EAAEC,iBAAiB;EAC9BC,UAAU;EACVC,QAAQ;EACRC,KAAK,EAAE7B,QAAQ;EACf8B,UAAU;EACVC,YAAY;EACZC,eAAe;EACfC,IAAI,EAAEC,UAAU;EAChBC,KAAK,EAAEC,WAAW;EAClBC,IAAI;EACJC,SAAS;EACTvC,YAAY;EACZwC,cAAc,GAAG5B,sBAAsB;EACvC6B,qBAAqB;EACrBC;AAC0B,CAAC,KAAK;EAChC,MAAMC,mBAAmB,GAAGzD,UAAU,CAAC0D,OAAO,CAAChB,UAAU,IAAI,CAAC,CAAC,CAAC,CAACiB,KAAK;EAEtE,MAAMnB,WAAW,GACfC,iBAAiB,KAAKmB,SAAS,GAC3BnB,iBAAiB,GACjB,OAAOgB,mBAAmB,KAAK,QAAQ,GACrCA,mBAAmB,GACnBhD,oBAAoB;EAC5B,MAAM6B,aAAa,GACjBC,mBAAmB,KAAKqB,SAAS,GAC7BrB,mBAAmB,GACnB,OAAOkB,mBAAmB,KAAK,QAAQ,GACrCA,mBAAmB,GACnB/C,sBAAsB;EAE9B,MAAMmD,aAAa,GAAGjD,gBAAgB,CAACC,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,CAAC;EACxE,MAAM+C,eAAe,GAAGrC,kBAAkB,CAACZ,QAAQ,EAAEC,YAAY,EAAEC,QAAQ,CAAC;EAE5E,MAAMiC,IAAI,GAAGnD,KAAK,CAACkE,OAAO,CAAC,MAAM;IAC/B,IAAI,CAACd,UAAU,EAAE;MACf,OAAO,IAAI;IACb;IAEA,MAAMe,YAAY,GAAGf,UAAU,CAAC;MAC9BgB,OAAO,EAAE,KAAK;MACdN,KAAK,EAAErB,aAAa;MACpB4B,IAAI,EAAEvD,SAAS;MACf6C;IACF,CAAC,CAAC;IAEF,MAAMW,UAAU,GAAGlB,UAAU,CAAC;MAC5BgB,OAAO,EAAE,IAAI;MACbN,KAAK,EAAEnB,WAAW;MAClB0B,IAAI,EAAEvD,SAAS;MACf6C;IACF,CAAC,CAAC;IAEF,oBACEhD,KAAA,CAACP,IAAI;MAACoC,KAAK,EAAE+B,MAAM,CAACpB,IAAK;MAAAqB,QAAA,gBACvB/D,IAAA,CAACR,QAAQ,CAACG,IAAI;QAACoC,KAAK,EAAE;UAAEiC,OAAO,EAAER;QAAgB,CAAE;QAAAO,QAAA,EAChDL;MAAY,CACA,CAAC,eAChB1D,IAAA,CAACR,QAAQ,CAACG,IAAI;QACZoC,KAAK,EAAE,CAACrC,UAAU,CAACuE,YAAY,EAAE;UAAED,OAAO,EAAET;QAAc,CAAC,CAAE;QAAAQ,QAAA,EAE5DF;MAAU,CACE,CAAC;IAAA,CACZ,CAAC;EAEX,CAAC,EAAE,CACD3B,WAAW,EACXqB,aAAa,EACbZ,UAAU,EACVX,aAAa,EACbwB,eAAe,EACfN,KAAK,CACN,CAAC;EAEF,MAAMgB,WAAW,GAAG3E,KAAK,CAAC4E,WAAW,CAClCR,OAAgB,IACfjC,WAAW,GACTA,WAAW,CAAC;IACViC,OAAO;IACPN,KAAK,EAAEM,OAAO,GAAGzB,WAAW,GAAGF,aAAa;IAC5CD,KAAK,EAAEK,UAAU;IACjBW,SAAS;IACTqB,gBAAgB,EAAEnB,qBAAqB;IACvCC;EACF,CAAC,CAAC,gBAEFlD,IAAA,CAACF,eAAe;IACduD,KAAK,EAAEM,OAAO,GAAGzB,WAAW,GAAGF,aAAc;IAC7CU,IAAI,EAAEA,IAAK;IACXjB,KAAK,EAAEsB,SAAU;IACjBhB,KAAK,EAAEK;EAAW,CACnB,CACF,EACH,CACEV,WAAW,EACXQ,WAAW,EACXE,UAAU,EACVW,SAAS,EACTE,qBAAqB,EACrBC,KAAK,EACLlB,aAAa,EACbU,IAAI,CAER,CAAC;EAED,MAAM2B,QAAQ,GAAG3E,UAAU,CAAC0D,OAAO,CAACrB,KAAK,CAAC;EAC1C,MAAMuC,UAAU,GAAGD,QAAQ,EAAEE,KAAK,KAAKjB,SAAS;EAEhD,MAAMkB,iBAAmC,GAAGF,UAAU,GAClD,IAAI,GACJ;IAAEC,KAAK,EAAE9B;EAAgB,CAAC;EAE9B,MAAMgC,SAAS,GACb,OAAOlD,kBAAkB,KAAK,WAAW,GAAGA,kBAAkB,GAAGwB,SAAS;EAE5E,oBACE/C,IAAA,CAACH,iBAAiB;IAChBmD,cAAc,EAAEA,cAAe;IAC/BrB,MAAM,EAAEA,MAAO;IACfH,UAAU,EAAEA,UAAW;IACvBkD,IAAI,EAAC,KAAK;IACV,cAAYD,SAAU;IACtB,iBAAe3C,SAAU;IACzBS,UAAU,EAAEA,UAAW;IACvBC,YAAY,EAAEA,YAAa;IAC3BmC,mBAAmB,EAAE,CAAE;IACvBtC,QAAQ,EAAEA,QAAS;IACnBR,OAAO,EAAEA,OAAQ;IACjBD,WAAW,EAAEA,WAAY;IACzBkB,IAAI,EAAEA,IAAK;IACXf,KAAK,EAAE,CAAC+B,MAAM,CAACc,SAAS,EAAEJ,iBAAiB,CAAE;IAAAT,QAAA,eAE7C7D,KAAA,CAACP,IAAI;MAACkF,aAAa,EAAC,MAAM;MAAC9C,KAAK,EAAE,CAAC+B,MAAM,CAACgB,IAAI,EAAET,QAAQ,CAAE;MAAAN,QAAA,GACvDrB,IAAI,eACLxC,KAAA,CAACP,IAAI;QAAAoE,QAAA,gBACH/D,IAAA,CAACR,QAAQ,CAACG,IAAI;UAACoC,KAAK,EAAE;YAAEiC,OAAO,EAAER;UAAgB,CAAE;UAAAO,QAAA,EAChDG,WAAW,CAAC,KAAK;QAAC,CACN,CAAC,eAChBlE,IAAA,CAACR,QAAQ,CAACG,IAAI;UACZoC,KAAK,EAAE,CAACrC,UAAU,CAACuE,YAAY,EAAE;YAAED,OAAO,EAAET;UAAc,CAAC,CAAE;UAAAQ,QAAA,EAE5DG,WAAW,CAAC,IAAI;QAAC,CACL,CAAC;MAAA,CACZ,CAAC,EACNrB,WAAW,IAAI,IAAI,gBAClB7C,IAAA,CAACL,IAAI;QAACoC,KAAK,EAAE+B,MAAM,CAAClB,KAAM;QAAAmB,QAAA,EAAElB,WAAW,CAAC;UAAEK;QAAM,CAAC;MAAC,CAAO,CAAC,GACxD,IAAI;IAAA,CACJ;EAAC,CACU,CAAC;AAExB,CAAC;AAED,MAAM6B,0BAA0B,gBAAGxF,KAAK,CAACyF,IAAI,CAC3C1D,kBACF,CAA8B;AAE9B,OAAO,SAAS2D,UAAUA,CAAkBC,KAAe,EAAE;EAC3D,MAAM;IAAErD,OAAO;IAAED,WAAW;IAAES,QAAQ;IAAE8C,eAAe;IAAEjC,KAAK;IAAE,GAAGkC;EAAK,CAAC,GACvEF,KAAK;EAEP,MAAMG,aAAa,GAAGzF,iBAAiB,CAACiC,OAAO,CAAC;EAChD,MAAMyD,iBAAiB,GAAG1F,iBAAiB,CAACgC,WAAW,CAAC;EACxD,MAAM2D,cAAc,GAAG3F,iBAAiB,CAACyC,QAAQ,GAAGA,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;EAExE,MAAM5B,QAAQ,GAAG0E,eAAe,CAACK,MAAM,CAACC,OAAO,CAACvC,KAAK,CAAC;EAEtD,oBACElD,IAAA,CAAC+E,0BAA0B;IAAA,GACrBK,IAAI;IACRvD,OAAO,EAAEwD,aAAc;IACvBhD,QAAQ,EAAEkD,cAAe;IACzB3D,WAAW,EAAE0D,iBAAkB;IAC/BxD,SAAS,EAAEqD,eAAe,CAAC7C,KAAK,KAAK7B,QAAS;IAC9CyC,KAAK,EAAEA,KAAM;IACbZ,KAAK,EAAE7B,QAAS;IAChBD,YAAY,EAAE2E,eAAe,CAACK,MAAM,CAAC3E;EAAO,CAC7C,CAAC;AAEN;AAEA,MAAMiD,MAAM,GAAGpE,UAAU,CAACgG,MAAM,CAAC;EAC/BhD,IAAI,EAAE;IACJiD,MAAM,EAAE;EACV,CAAC;EACDb,IAAI,EAAE;IACJc,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE;EACb,CAAC;EACDpD,KAAK,EAAE;IACLrC,QAAQ,EAAE,UAAU;IACpB0F,GAAG,EAAE,CAAC;IACNC,GAAG,EAAE;EACP,CAAC;EACDtB,SAAS,EAAE;IACT;IACA;IACAuB,eAAe,EAAE,aAAa;IAC9B,GAAG1G,QAAQ,CAAC2G,MAAM,CAAC;MACjB;MACAC,GAAG,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAC;MACzBC,OAAO,EAAE;IACX,CAAC;EACH;AACF,CAAC,CAAC", "ignoreList": []}