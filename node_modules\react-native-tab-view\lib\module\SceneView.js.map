{"version": 3, "names": ["React", "StyleSheet", "View", "jsx", "_jsx", "SceneView", "children", "navigationState", "lazy", "layout", "index", "lazyPreloadDistance", "addEnterListener", "style", "isLoading", "setIsLoading", "useState", "Math", "abs", "useEffect", "handleEnter", "value", "prevState", "unsubscribe", "timer", "setTimeout", "clearTimeout", "focused", "styles", "route", "width", "absoluteFill", "loading", "create", "flex", "overflow"], "sourceRoot": "../../src", "sources": ["SceneView.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAyBC,UAAU,EAAEC,IAAI,QAAwB,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAmBhF,OAAO,SAASC,SAASA,CAAkB;EACzCC,QAAQ;EACRC,eAAe;EACfC,IAAI;EACJC,MAAM;EACNC,KAAK;EACLC,mBAAmB;EACnBC,gBAAgB;EAChBC;AACQ,CAAC,EAAE;EACX,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,KAAK,CAACgB,QAAQ,CAC9CC,IAAI,CAACC,GAAG,CAACX,eAAe,CAACG,KAAK,GAAGA,KAAK,CAAC,GAAGC,mBAC5C,CAAC;EAED,IACEG,SAAS,IACTG,IAAI,CAACC,GAAG,CAACX,eAAe,CAACG,KAAK,GAAGA,KAAK,CAAC,IAAIC,mBAAmB,EAC9D;IACA;IACAI,YAAY,CAAC,KAAK,CAAC;EACrB;EAEAf,KAAK,CAACmB,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAIC,KAAa,IAAK;MACrC;MACA,IAAIA,KAAK,KAAKX,KAAK,EAAE;QACnBK,YAAY,CAAEO,SAAS,IAAK;UAC1B,IAAIA,SAAS,EAAE;YACb,OAAO,KAAK;UACd;UACA,OAAOA,SAAS;QAClB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,IAAIC,WAAqC;IACzC,IAAIC,KAAgD;IAEpD,IAAIhB,IAAI,IAAIM,SAAS,EAAE;MACrB;MACAS,WAAW,GAAGX,gBAAgB,CAACQ,WAAW,CAAC;IAC7C,CAAC,MAAM,IAAIN,SAAS,EAAE;MACpB;MACA;MACAU,KAAK,GAAGC,UAAU,CAAC,MAAMV,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAClD;IAEA,OAAO,MAAM;MACXQ,WAAW,GAAG,CAAC;MACfG,YAAY,CAACF,KAAK,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAACZ,gBAAgB,EAAEF,KAAK,EAAEI,SAAS,EAAEN,IAAI,CAAC,CAAC;EAE9C,MAAMmB,OAAO,GAAGpB,eAAe,CAACG,KAAK,KAAKA,KAAK;EAE/C,oBACEN,IAAA,CAACF,IAAI;IACH,eAAa,CAACyB,OAAQ;IACtBd,KAAK,EAAE,CACLe,MAAM,CAACC,KAAK;IACZ;IACA;IACApB,MAAM,CAACqB,KAAK,GACR;MAAEA,KAAK,EAAErB,MAAM,CAACqB;IAAM,CAAC,GACvBH,OAAO,GACL1B,UAAU,CAAC8B,YAAY,GACvB,IAAI,EACVlB,KAAK,CACL;IAAAP,QAAA;IAGA;IACA;IACA;IACAqB,OAAO,IAAIlB,MAAM,CAACqB,KAAK,GAAGxB,QAAQ,CAAC;MAAE0B,OAAO,EAAElB;IAAU,CAAC,CAAC,GAAG;EAAI,CAE/D,CAAC;AAEX;AAEA,MAAMc,MAAM,GAAG3B,UAAU,CAACgC,MAAM,CAAC;EAC/BJ,KAAK,EAAE;IACLK,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}