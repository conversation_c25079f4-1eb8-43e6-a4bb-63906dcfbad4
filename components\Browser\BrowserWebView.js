import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Dimensions,
  StatusBar,
  Text,
  Modal,
  ScrollView
} from 'react-native';
import { WebView } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width, height } = Dimensions.get('window');

const BrowserWebView = ({
  initialUrl = 'https://www.google.com',
  onVideoDetected,
  onDownloadRequest,
  onTitleChange,
  onUrlChange,
  showDownloadButton = true
}) => {
  const [url, setUrl] = useState(initialUrl);
  const [currentUrl, setCurrentUrl] = useState(initialUrl);
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [history, setHistory] = useState([]);
  const [bookmarks, setBookmarks] = useState([]);
  const [showBookmarks, setShowBookmarks] = useState(false);
  const [pageTitle, setPageTitle] = useState('');
  const [isSecure, setIsSecure] = useState(false);
  
  const webViewRef = useRef(null);

  useEffect(() => {
    loadBookmarks();
  }, []);

  const loadBookmarks = async () => {
    try {
      const savedBookmarks = await AsyncStorage.getItem('browser_bookmarks');
      if (savedBookmarks) {
        setBookmarks(JSON.parse(savedBookmarks));
      }
    } catch (error) {
      console.error('Erro ao carregar favoritos:', error);
    }
  };

  const saveBookmarks = async (newBookmarks) => {
    try {
      await AsyncStorage.setItem('browser_bookmarks', JSON.stringify(newBookmarks));
      setBookmarks(newBookmarks);
    } catch (error) {
      console.error('Erro ao salvar favoritos:', error);
    }
  };

  const addBookmark = () => {
    const newBookmark = {
      id: Date.now().toString(),
      title: pageTitle || currentUrl,
      url: currentUrl,
      timestamp: new Date().toISOString()
    };
    
    const updatedBookmarks = [...bookmarks, newBookmark];
    saveBookmarks(updatedBookmarks);
    Alert.alert('Sucesso', 'Página adicionada aos favoritos!');
  };

  const removeBookmark = (id) => {
    const updatedBookmarks = bookmarks.filter(bookmark => bookmark.id !== id);
    saveBookmarks(updatedBookmarks);
  };

  const navigateToUrl = (targetUrl) => {
    let formattedUrl = targetUrl.trim();
    
    // Adicionar protocolo se necessário
    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      // Se parece ser uma busca, usar Google
      if (!formattedUrl.includes('.') || formattedUrl.includes(' ')) {
        formattedUrl = `https://www.google.com/search?q=${encodeURIComponent(formattedUrl)}`;
      } else {
        formattedUrl = `https://${formattedUrl}`;
      }
    }
    
    setUrl(formattedUrl);
    setCurrentUrl(formattedUrl);
  };

  const handleNavigationStateChange = (navState) => {
    setCurrentUrl(navState.url);
    setCanGoBack(navState.canGoBack);
    setCanGoForward(navState.canGoForward);
    setLoading(navState.loading);
    setPageTitle(navState.title);
    setIsSecure(navState.url.startsWith('https://'));

    // Notificar mudanças para o componente pai (TabBrowser)
    if (onTitleChange) onTitleChange(navState.title);
    if (onUrlChange) onUrlChange(navState.url);

    // Adicionar ao histórico
    if (!loading && navState.url !== currentUrl) {
      setHistory(prev => {
        const newHistory = [...prev, {
          url: navState.url,
          title: navState.title,
          timestamp: new Date().toISOString()
        }];
        return newHistory.slice(-50); // Manter apenas os últimos 50
      });
    }
  };

  const detectVideoInPage = (html) => {
    // Detectar vídeos na página
    const videoPatterns = [
      /\.mp4/gi,
      /\.webm/gi,
      /\.avi/gi,
      /\.mov/gi,
      /youtube\.com\/watch/gi,
      /vimeo\.com/gi,
      /tiktok\.com/gi,
      /instagram\.com.*\/p\//gi,
      /facebook\.com.*\/videos/gi
    ];

    const hasVideo = videoPatterns.some(pattern => pattern.test(html));
    if (hasVideo && onVideoDetected) {
      onVideoDetected(currentUrl);
    }
  };

  const handleMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'pageContent') {
        detectVideoInPage(data.content);
      }
    } catch (error) {
      console.error('Erro ao processar mensagem:', error);
    }
  };

  const injectedJavaScript = `
    (function() {
      // Enviar conteúdo da página para detecção de vídeo
      setTimeout(() => {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'pageContent',
          content: document.documentElement.outerHTML
        }));
      }, 2000);
      
      // Ad Block básico
      const adSelectors = [
        '[id*="ad"]',
        '[class*="ad"]',
        '[id*="banner"]',
        '[class*="banner"]',
        'iframe[src*="doubleclick"]',
        'iframe[src*="googlesyndication"]',
        '.advertisement',
        '.ads',
        '.ad-container'
      ];
      
      adSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          if (el) el.style.display = 'none';
        });
      });
    })();
    true;
  `;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      
      {/* Barra de navegação */}
      <View style={styles.navigationBar}>
        <View style={styles.urlContainer}>
          <View style={styles.securityIndicator}>
            <Ionicons 
              name={isSecure ? "lock-closed" : "lock-open"} 
              size={16} 
              color={isSecure ? "#4CAF50" : "#FF9800"} 
            />
          </View>
          
          <TextInput
            style={styles.urlInput}
            value={url}
            onChangeText={setUrl}
            onSubmitEditing={() => navigateToUrl(url)}
            placeholder="Digite uma URL ou pesquise..."
            placeholderTextColor="#888"
            autoCapitalize="none"
            autoCorrect={false}
            selectTextOnFocus
          />
          
          <TouchableOpacity 
            style={styles.refreshButton}
            onPress={() => webViewRef.current?.reload()}
          >
            <Ionicons name="refresh" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
        
        <View style={styles.navigationButtons}>
          <TouchableOpacity 
            style={[styles.navButton, !canGoBack && styles.navButtonDisabled]}
            onPress={() => webViewRef.current?.goBack()}
            disabled={!canGoBack}
          >
            <Ionicons name="arrow-back" size={20} color={canGoBack ? "#fff" : "#666"} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.navButton, !canGoForward && styles.navButtonDisabled]}
            onPress={() => webViewRef.current?.goForward()}
            disabled={!canGoForward}
          >
            <Ionicons name="arrow-forward" size={20} color={canGoForward ? "#fff" : "#666"} />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.navButton}
            onPress={() => setShowMenu(true)}
          >
            <Ionicons name="menu" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Botão de download flutuante */}
      {showDownloadButton && (
        <TouchableOpacity 
          style={styles.downloadButton}
          onPress={() => onDownloadRequest && onDownloadRequest(currentUrl)}
        >
          <Ionicons name="download" size={24} color="#fff" />
        </TouchableOpacity>
      )}

      {/* WebView */}
      <WebView
        ref={webViewRef}
        source={{ uri: url }}
        style={styles.webview}
        onNavigationStateChange={handleNavigationStateChange}
        onMessage={handleMessage}
        injectedJavaScript={injectedJavaScript}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        allowsBackForwardNavigationGestures={true}
        userAgent="Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36"
      />

      {/* Menu do navegador */}
      <Modal
        visible={showMenu}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMenu(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.menuContainer}>
            <View style={styles.menuHeader}>
              <Text style={styles.menuTitle}>Menu do Navegador</Text>
              <TouchableOpacity onPress={() => setShowMenu(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.menuContent}>
              <TouchableOpacity style={styles.menuItem} onPress={addBookmark}>
                <Ionicons name="bookmark" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Adicionar aos Favoritos</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.menuItem} 
                onPress={() => {
                  setShowMenu(false);
                  setShowBookmarks(true);
                }}
              >
                <Ionicons name="bookmarks" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Ver Favoritos</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.menuItem}>
                <Ionicons name="time" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Histórico</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.menuItem}>
                <Ionicons name="settings" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Configurações</Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Modal de favoritos */}
      <Modal
        visible={showBookmarks}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowBookmarks(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.bookmarksContainer}>
            <View style={styles.menuHeader}>
              <Text style={styles.menuTitle}>Favoritos</Text>
              <TouchableOpacity onPress={() => setShowBookmarks(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.bookmarksList}>
              {bookmarks.map((bookmark) => (
                <View key={bookmark.id} style={styles.bookmarkItem}>
                  <TouchableOpacity 
                    style={styles.bookmarkContent}
                    onPress={() => {
                      navigateToUrl(bookmark.url);
                      setShowBookmarks(false);
                    }}
                  >
                    <Text style={styles.bookmarkTitle}>{bookmark.title}</Text>
                    <Text style={styles.bookmarkUrl}>{bookmark.url}</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.deleteBookmark}
                    onPress={() => removeBookmark(bookmark.id)}
                  >
                    <Ionicons name="trash" size={16} color="#ff4444" />
                  </TouchableOpacity>
                </View>
              ))}
              
              {bookmarks.length === 0 && (
                <Text style={styles.emptyMessage}>Nenhum favorito salvo</Text>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  navigationBar: {
    flexDirection: 'row',
    backgroundColor: '#1a1a2e',
    paddingHorizontal: 10,
    paddingVertical: 8,
    alignItems: 'center',
  },
  urlContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#2d2d44',
    borderRadius: 25,
    alignItems: 'center',
    paddingHorizontal: 12,
    marginRight: 10,
  },
  securityIndicator: {
    marginRight: 8,
  },
  urlInput: {
    flex: 1,
    color: '#fff',
    fontSize: 14,
    paddingVertical: 10,
  },
  refreshButton: {
    padding: 5,
  },
  navigationButtons: {
    flexDirection: 'row',
  },
  navButton: {
    padding: 8,
    marginLeft: 5,
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  downloadButton: {
    position: 'absolute',
    right: 20,
    bottom: 100,
    backgroundColor: '#6c5ce7',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
  },
  webview: {
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  menuContainer: {
    backgroundColor: '#1a1a2e',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.6,
  },
  bookmarksContainer: {
    backgroundColor: '#1a1a2e',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.8,
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  menuTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  menuContent: {
    padding: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  menuItemText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 15,
  },
  bookmarksList: {
    padding: 20,
  },
  bookmarkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  bookmarkContent: {
    flex: 1,
  },
  bookmarkTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bookmarkUrl: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  deleteBookmark: {
    padding: 10,
  },
  emptyMessage: {
    color: '#888',
    textAlign: 'center',
    marginTop: 50,
    fontSize: 16,
  },
});

export default BrowserWebView;
