{"version": 3, "names": ["React", "Animated", "Easing", "Platform", "StyleSheet", "useAnimatedValue", "jsx", "_jsx", "useNativeDriver", "OS", "getTranslateX", "position", "routes", "getTabWidth", "direction", "gap", "width", "inputRange", "map", "_", "i", "outputRange", "reduce", "acc", "sumTabWidth", "j", "translateX", "interpolate", "extrapolate", "multiply", "TabBarIndicator", "layout", "navigationState", "style", "children", "isIndicatorShown", "useRef", "isWidthDynamic", "opacity", "indicatorVisible", "slice", "index", "every", "r", "useEffect", "fadeInIndicator", "current", "timing", "toValue", "duration", "easing", "in", "linear", "start", "stopAnimation", "transform", "length", "push", "scaleX", "styleList", "left", "View", "styles", "indicator", "create", "backgroundColor", "bottom", "end", "height"], "sourceRoot": "../../src", "sources": ["TabBarIndicator.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,MAAM,EACNC,QAAQ,EAERC,UAAU,QAEL,cAAc;AAQrB,SAASC,gBAAgB,QAAQ,uBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AActD,MAAMC,eAAe,GAAGL,QAAQ,CAACM,EAAE,KAAK,KAAK;AAE7C,MAAMC,aAAa,GAAGA,CACpBC,QAAgD,EAChDC,MAAe,EACfC,WAAwB,EACxBC,SAA0B,EAC1BC,GAAY,EACZC,KAAuB,KACpB;EACH,MAAMC,UAAU,GAAGL,MAAM,CAACM,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;;EAE1C;EACA,MAAMC,WAAW,GAAGT,MAAM,CAACU,MAAM,CAAW,CAACC,GAAG,EAAEJ,CAAC,EAAEC,CAAC,KAAK;IACzD,IAAI,OAAOJ,KAAK,KAAK,QAAQ,EAAE;MAC7B,IAAII,CAAC,KAAK,CAAC,EAAE,OAAO,CAACP,WAAW,CAACO,CAAC,CAAC,GAAG,CAAC,GAAGJ,KAAK,GAAG,CAAC,CAAC;MAEpD,IAAIQ,WAAW,GAAG,CAAC;MACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAEK,CAAC,EAAE,EAAE;QAC1BD,WAAW,IAAIX,WAAW,CAACY,CAAC,CAAC;MAC/B;MAEA,OAAO,CACL,GAAGF,GAAG,EACNC,WAAW,GAAGX,WAAW,CAACO,CAAC,CAAC,GAAG,CAAC,IAAIL,GAAG,GAAGA,GAAG,GAAGK,CAAC,GAAG,CAAC,CAAC,GAAGJ,KAAK,GAAG,CAAC,CACnE;IACH,CAAC,MAAM;MACL,IAAII,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;MACvB,OAAO,CAAC,GAAGG,GAAG,EAAEA,GAAG,CAACH,CAAC,GAAG,CAAC,CAAC,GAAGP,WAAW,CAACO,CAAC,GAAG,CAAC,CAAC,IAAIL,GAAG,IAAI,CAAC,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,UAAU,GAAGf,QAAQ,CAACgB,WAAW,CAAC;IACtCV,UAAU;IACVI,WAAW;IACXO,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,OAAO3B,QAAQ,CAAC4B,QAAQ,CAACH,UAAU,EAAEZ,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACpE,CAAC;AAED,OAAO,SAASgB,eAAeA,CAAkB;EAC/CjB,WAAW;EACXkB,MAAM;EACNC,eAAe;EACfrB,QAAQ;EACRK,KAAK;EACLF,SAAS;EACTC,GAAG;EACHkB,KAAK;EACLC;AACQ,CAAC,EAAE;EACX,MAAMC,gBAAgB,GAAGnC,KAAK,CAACoC,MAAM,CAAC,KAAK,CAAC;EAC5C,MAAMC,cAAc,GAAGrB,KAAK,KAAK,MAAM;EAEvC,MAAMsB,OAAO,GAAGjC,gBAAgB,CAACgC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;EAExD,MAAME,gBAAgB,GAAGF,cAAc,GACnCN,MAAM,CAACf,KAAK,IACZgB,eAAe,CAACpB,MAAM,CACnB4B,KAAK,CAAC,CAAC,EAAER,eAAe,CAACS,KAAK,CAAC,CAC/BC,KAAK,CAAC,CAACvB,CAAC,EAAEwB,CAAC,KAAK9B,WAAW,CAAC8B,CAAC,CAAC,CAAC,GAClC,IAAI;EAER3C,KAAK,CAAC4C,SAAS,CAAC,MAAM;IACpB,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B,IACE,CAACV,gBAAgB,CAACW,OAAO,IACzBT,cAAc;MACd;MACAE,gBAAgB,EAChB;QACAJ,gBAAgB,CAACW,OAAO,GAAG,IAAI;QAE/B7C,QAAQ,CAAC8C,MAAM,CAACT,OAAO,EAAE;UACvBU,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAE,GAAG;UACbC,MAAM,EAAEhD,MAAM,CAACiD,EAAE,CAACjD,MAAM,CAACkD,MAAM,CAAC;UAChC5C;QACF,CAAC,CAAC,CAAC6C,KAAK,CAAC,CAAC;MACZ;IACF,CAAC;IAEDR,eAAe,CAAC,CAAC;IAEjB,OAAO,MAAMP,OAAO,CAACgB,aAAa,CAAC,CAAC;EACtC,CAAC,EAAE,CAACf,gBAAgB,EAAEF,cAAc,EAAEC,OAAO,CAAC,CAAC;EAE/C,MAAM;IAAE1B;EAAO,CAAC,GAAGoB,eAAe;EAElC,MAAMuB,SAAS,GAAG,EAAE;EAEpB,IAAIxB,MAAM,CAACf,KAAK,EAAE;IAChB,MAAMU,UAAU,GACdd,MAAM,CAAC4C,MAAM,GAAG,CAAC,GACb9C,aAAa,CAACC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,GAAG,EAAEC,KAAK,CAAC,GACnE,CAAC;IAEPuC,SAAS,CAACE,IAAI,CAAC;MAAE/B;IAAW,CAAC,CAAC;EAChC;EAEA,IAAIV,KAAK,KAAK,MAAM,EAAE;IACpB,MAAMC,UAAU,GAAGL,MAAM,CAACM,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC;IAC1C,MAAMC,WAAW,GAAGJ,UAAU,CAACC,GAAG,CAACL,WAAW,CAAC;IAE/C0C,SAAS,CAACE,IAAI,CACZ;MACEC,MAAM,EACJ9C,MAAM,CAAC4C,MAAM,GAAG,CAAC,GACb7C,QAAQ,CAACgB,WAAW,CAAC;QACnBV,UAAU;QACVI,WAAW;QACXO,WAAW,EAAE;MACf,CAAC,CAAC,GACFP,WAAW,CAAC,CAAC;IACrB,CAAC,EACD;MAAEK,UAAU,EAAEZ,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,GAAG;IAAI,CACjD,CAAC;EACH;EAEA,MAAM6C,SAA+B,GAAG,EAAE;;EAE1C;EACA,IAAIxD,QAAQ,CAACM,EAAE,KAAK,KAAK,IAAIO,KAAK,KAAK,MAAM,EAAE;IAC7C2C,SAAS,CAACF,IAAI,CACZ;MAAEzC,KAAK,EAAEuC,SAAS,CAAC,CAAC,CAAC,CAACG;IAAO,CAAC,EAC9B;MAAEE,IAAI,EAAEL,SAAS,CAAC,CAAC,CAAC,CAAC7B;IAAW,CAClC,CAAC;EACH,CAAC,MAAM;IACLiC,SAAS,CAACF,IAAI,CACZ;MAAEzC,KAAK,EAAEA,KAAK,KAAK,MAAM,GAAG,CAAC,GAAGA;IAAM,CAAC,EACvC;MAAEqC,KAAK,EAAE,GAAI,GAAG,GAAGzC,MAAM,CAAC4C,MAAM,GAAIxB,eAAe,CAACS,KAAK;IAAI,CAAC,EAC9D;MAAEc;IAAU,CACd,CAAC;EACH;EAEA,oBACEhD,IAAA,CAACN,QAAQ,CAAC4D,IAAI;IACZ5B,KAAK,EAAE,CACL6B,MAAM,CAACC,SAAS,EAChBJ,SAAS,EACT3C,KAAK,KAAK,MAAM,GAAG;MAAEsB,OAAO,EAAEA;IAAQ,CAAC,GAAG,IAAI,EAC9CL,KAAK,CACL;IAAAC,QAAA,EAEDA;EAAQ,CACI,CAAC;AAEpB;AAEA,MAAM4B,MAAM,GAAG1D,UAAU,CAAC4D,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,eAAe,EAAE,SAAS;IAC1BtD,QAAQ,EAAE,UAAU;IACpB0C,KAAK,EAAE,CAAC;IACRa,MAAM,EAAE,CAAC;IACTC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE;EACV;AACF,CAAC,CAAC", "ignoreList": []}