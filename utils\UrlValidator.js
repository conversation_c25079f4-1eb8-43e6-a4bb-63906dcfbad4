class UrlValidator {
  constructor() {
    // Removido: lista de domínios suportados - agora aceita qualquer domínio

    this.videoExtensions = [
      '.mp4', '.avi', '.mov', '.wmv', '.flv',
      '.webm', '.mkv', '.m4v', '.3gp', '.ogv'
    ];
  }

  // Validação básica de URL
  isValidUrl(url) {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  // Aceitar qualquer domínio válido
  isSupportedDomain(url) {
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
      // Aceita qualquer domínio válido - sem restrições
      return true;
    } catch {
      return false;
    }
  }

  // Verificar se é um link direto de vídeo
  isDirectVideoUrl(url) {
    const lowerUrl = url.toLowerCase();
    return this.videoExtensions.some(ext => lowerUrl.includes(ext));
  }

  // Normalizar URL
  normalizeUrl(url) {
    try {
      // Adicionar protocolo se não existir
      if (!url.startsWith('http')) {
        url = `https://${url}`;
      }

      const urlObj = new URL(url);
      
      // Normalizar YouTube URLs
      if (urlObj.hostname.includes('youtube.com')) {
        const videoId = urlObj.searchParams.get('v');
        if (videoId) {
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }
      
      // Converter youtu.be para youtube.com
      if (urlObj.hostname.includes('youtu.be')) {
        const videoId = urlObj.pathname.slice(1).split('?')[0];
        if (videoId) {
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }

      // Normalizar Vimeo URLs
      if (urlObj.hostname.includes('vimeo.com')) {
        const videoId = urlObj.pathname.split('/').pop();
        if (videoId && /^\d+$/.test(videoId)) {
          return `https://vimeo.com/${videoId}`;
        }
      }

      return url;
    } catch {
      return url;
    }
  }

  // Extrair informações da URL
  extractUrlInfo(url) {
    try {
      const normalizedUrl = this.normalizeUrl(url);
      const urlObj = new URL(normalizedUrl);
      const hostname = urlObj.hostname.toLowerCase().replace('www.', '');

      let platform = 'unknown';
      let videoId = null;

      // Identificar plataforma
      if (hostname.includes('youtube.com')) {
        platform = 'youtube';
        videoId = urlObj.searchParams.get('v');
      } else if (hostname.includes('vimeo.com')) {
        platform = 'vimeo';
        videoId = urlObj.pathname.split('/').pop();
      } else if (hostname.includes('tiktok.com')) {
        platform = 'tiktok';
        const match = urlObj.pathname.match(/\/video\/(\d+)/);
        videoId = match ? match[1] : null;
      } else if (hostname.includes('instagram.com')) {
        platform = 'instagram';
        const match = urlObj.pathname.match(/\/(p|reel)\/([^\/]+)/);
        videoId = match ? match[2] : null;
      } else if (hostname.includes('facebook.com') || hostname.includes('fb.watch')) {
        platform = 'facebook';
      } else if (hostname.includes('twitter.com') || hostname.includes('x.com')) {
        platform = 'twitter';
      } else if (this.isDirectVideoUrl(url)) {
        platform = 'direct';
      }

      return {
        originalUrl: url,
        normalizedUrl,
        hostname,
        platform,
        videoId,
        isSupported: true, // Sempre true - aceita qualquer URL válida
        isDirect: this.isDirectVideoUrl(url)
      };
    } catch (error) {
      return {
        originalUrl: url,
        normalizedUrl: url,
        hostname: null,
        platform: 'unknown',
        videoId: null,
        isSupported: false,
        isDirect: false,
        error: error.message
      };
    }
  }

  // Validar e dar feedback sobre a URL
  validateAndGetFeedback(url) {
    if (!url || !url.trim()) {
      return {
        isValid: false,
        message: 'Por favor, insira um link',
        type: 'empty'
      };
    }

    const trimmedUrl = url.trim();
    
    if (!this.isValidUrl(trimmedUrl)) {
      return {
        isValid: false,
        message: 'URL inválida. Verifique se o link está correto.',
        type: 'invalid_format'
      };
    }

    const urlInfo = this.extractUrlInfo(trimmedUrl);
    
    if (urlInfo.error) {
      return {
        isValid: false,
        message: 'Erro ao processar a URL',
        type: 'processing_error'
      };
    }

    // Aceitar qualquer URL válida - sem restrições de site
    return {
      isValid: true,
      message: `Link válido detectado: ${urlInfo.hostname || 'URL válida'}`,
      type: 'valid',
      urlInfo
    };
  }

  // Sugerir correções para URLs comuns
  suggestCorrections(url) {
    const suggestions = [];
    
    // Sugestões para YouTube
    if (url.includes('youtube') && !url.includes('youtube.com')) {
      suggestions.push('Você quis dizer: youtube.com?');
    }
    
    // Sugestões para links sem protocolo
    if (!url.startsWith('http') && this.isValidUrl(`https://${url}`)) {
      suggestions.push(`Tente: https://${url}`);
    }

    // Sugestões para domínios comuns
    const commonDomains = ['youtube.com', 'vimeo.com', 'tiktok.com'];
    commonDomains.forEach(domain => {
      if (url.toLowerCase().includes(domain.split('.')[0]) && !url.includes(domain)) {
        suggestions.push(`Você quis dizer: ${domain}?`);
      }
    });

    return suggestions;
  }
}

export default new UrlValidator();
