import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
  Dimensions,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import BrowserWebView from './BrowserWebView';

const { width } = Dimensions.get('window');

const TabBrowser = ({ onVideoDetected, onDownloadRequest }) => {
  const [tabs, setTabs] = useState([
    {
      id: '1',
      title: 'Nova Aba',
      url: 'https://www.google.com',
      isActive: true
    }
  ]);
  const [activeTabId, setActiveTabId] = useState('1');

  const createNewTab = (url = 'https://www.google.com') => {
    const newTab = {
      id: Date.now().toString(),
      title: 'Nova Aba',
      url: url,
      isActive: false
    };
    
    setTabs(prevTabs => [...prevTabs, newTab]);
    switchToTab(newTab.id);
  };

  const closeTab = (tabId) => {
    if (tabs.length === 1) {
      // Se for a última aba, criar uma nova
      createNewTab();
      return;
    }

    const tabIndex = tabs.findIndex(tab => tab.id === tabId);
    const newTabs = tabs.filter(tab => tab.id !== tabId);
    
    // Se a aba fechada era a ativa, ativar outra
    if (tabId === activeTabId) {
      const newActiveIndex = tabIndex > 0 ? tabIndex - 1 : 0;
      const newActiveTab = newTabs[newActiveIndex];
      setActiveTabId(newActiveTab.id);
    }
    
    setTabs(newTabs);
  };

  const switchToTab = (tabId) => {
    setActiveTabId(tabId);
    setTabs(prevTabs => 
      prevTabs.map(tab => ({
        ...tab,
        isActive: tab.id === tabId
      }))
    );
  };

  const updateTabTitle = (tabId, title) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.id === tabId ? { ...tab, title: title || 'Carregando...' } : tab
      )
    );
  };

  const updateTabUrl = (tabId, url) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.id === tabId ? { ...tab, url } : tab
      )
    );
  };

  const getTabTitle = (title, url) => {
    if (title && title !== 'Carregando...' && title !== 'Nova Aba') {
      return title.length > 15 ? title.substring(0, 15) + '...' : title;
    }
    
    if (url) {
      try {
        const domain = new URL(url).hostname.replace('www.', '');
        return domain.length > 15 ? domain.substring(0, 15) + '...' : domain;
      } catch {
        return 'Nova Aba';
      }
    }
    
    return 'Nova Aba';
  };

  const activeTab = tabs.find(tab => tab.id === activeTabId);

  return (
    <View style={styles.container}>
      {/* Barra de abas com safe area */}
      <SafeAreaView edges={['left', 'right']} style={styles.tabBarContainer}>
        <View style={styles.tabBar}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.tabsContainer}
            contentContainerStyle={styles.tabsContent}
          >
            {tabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[
                  styles.tab,
                  tab.id === activeTabId && styles.activeTab
                ]}
                onPress={() => switchToTab(tab.id)}
              >
                <Text style={[
                  styles.tabTitle,
                  tab.id === activeTabId && styles.activeTabTitle
                ]}>
                  {getTabTitle(tab.title, tab.url)}
                </Text>

                <TouchableOpacity
                  style={styles.closeTabButton}
                  onPress={() => closeTab(tab.id)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Ionicons name="close" size={14} color="#888" />
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </ScrollView>

          <TouchableOpacity
            style={styles.newTabButton}
            onPress={() => createNewTab()}
          >
            <Ionicons name="add" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>

      {/* Conteúdo da aba ativa */}
      {activeTab && (
        <BrowserWebView
          key={activeTab.id}
          initialUrl={activeTab.url}
          onVideoDetected={onVideoDetected}
          onDownloadRequest={onDownloadRequest}
          onTitleChange={(title) => updateTabTitle(activeTab.id, title)}
          onUrlChange={(url) => updateTabUrl(activeTab.id, url)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  tabBarContainer: {
    backgroundColor: '#1a1a2e',
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  tabBar: {
    flexDirection: 'row',
    paddingTop: 5,
    paddingHorizontal: 5,
  },
  tabsContainer: {
    flex: 1,
  },
  tabsContent: {
    paddingHorizontal: 5,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2d2d44',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 2,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    minWidth: 120,
    maxWidth: 180,
  },
  activeTab: {
    backgroundColor: '#1a1a2e',
    borderBottomWidth: 2,
    borderBottomColor: '#6c5ce7',
  },
  tabTitle: {
    color: '#888',
    fontSize: 12,
    flex: 1,
    marginRight: 8,
  },
  activeTabTitle: {
    color: '#fff',
    fontWeight: 'bold',
  },
  closeTabButton: {
    padding: 2,
  },
  newTabButton: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 8,
    backgroundColor: '#2d2d44',
    marginRight: 5,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
});

export default TabBrowser;
