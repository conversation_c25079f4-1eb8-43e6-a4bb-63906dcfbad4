{"name": "react-native-pager-view", "version": "6.8.1", "description": "React Native wrapper for Android and iOS ViewPager", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib", "android", "ios", "react-native-pager-view.podspec", "!lib/typescript/example", "!android/build", "!ios/build", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__"], "scripts": {"test": "jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "prepare": "bob build", "release": "release-it", "release:next": "release-it --npm.tag=next --preRelease=rc", "bootstrap": "cd example; bun install && bun pods ", "bootstrap:no:pods": "bun install --cwd example", "test:e2e:android": "bash ./scripts/run-maestro-tests.sh android", "test:e2e:ios": "bash ./scripts/run-maestro-tests.sh ios", "example:start": "cd example; bun start", "example:android": "cd example; bun android", "example:ios": "cd example; bun ios", "example:android:release": "cd example;react-native run-android --mode \"Release\" --appId com.pagerviewexample", "example:ios:release": "cd example;react-native run-ios --mode \"Release\""}, "keywords": ["react-native", "ios", "android"], "repository": "https://github.com/callstack/react-native-pager-view", "author": "tro<PERSON><PERSON> <<EMAIL>> (https://github.com/callstack)", "license": "MIT", "bugs": {"url": "https://github.com/callstack/react-native-pager-view/issues"}, "homepage": "https://github.com/callstack/react-native-pager-view#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/eslint-parser": "^7.20.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@release-it/conventional-changelog": "^2.0.0", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "eslint": "^8.19.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^28.9.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.6.3", "prettier": "2.8.8", "react": "19.0.0", "react-native": "0.79.2", "react-native-builder-bob": "^0.40.5", "release-it": "^14.2.2", "typescript": "5.0.4"}, "peerDependencies": {"react": "*", "react-native": "*"}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "eslintConfig": {"root": true, "parser": "@babel/eslint-parser", "extends": ["@react-native-community", "prettier"], "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "codegenConfig": {"name": "pagerview", "type": "components", "jsSrcsDir": "src", "android": {"javaPackageName": "com.reactnativepagerview"}, "ios": {"componentProvider": {"RNCViewPager": "RNCPagerViewComponentView"}}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}}