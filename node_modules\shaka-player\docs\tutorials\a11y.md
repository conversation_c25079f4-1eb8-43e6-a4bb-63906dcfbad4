# Creating accessible custom buttons.


All Shaka-provided UI elements follow the [Web Accessibility Standards][],
meaning that people who use screen readers to surf the web still get their full
functionality even though they might not be able to see the elements.

Creating accessible buttons is really easy, and we encourage all applications
to do it. With just a few lines of code, your custom elements can become
accessible for everyone!

We use [ARIA labels][] to tell screen readers what the button is for.
Let's take our skip button from the Customization tutorial and add an aria label to it:

```js
// skipButton.js

// Use shaka.ui.Element as a base class
myapp.SkipButton = class extends shaka.ui.Element {
  constructor(parent, controls) {
    super(parent, controls);

    // The actual button that will be displayed
    this.button_ = document.createElement('button');
    this.button_.textContent = 'Skip current video';
    // Screen readers will read "skip button"
    this.button_.setAttribute('aria-label', 'Skip');
    this.parent.appendChild(this.button_);
  }
};
```

Please note that you might need to update the aria label if the button changes its purpose.
Let's say, we want the skip button to take us to the first video of the playlist if we're
currently watching the last one. We can change the button's icon to visually emphasize
new behavior. These changes, however, won't affect someone using a screen reader, so we
will need to change the aria-label as well.

```js
this.eventManager.listen(this.button_, 'click', () => {
  const nextManifest = /* Your logic to pick the next video to be played */
    myapp.getNextManifest();

  this.player.load(nextManifest);

  if (this.isLastManifest(nextManifest)) {
    // This is the last video. Change icon and aria label.
    this.button_.icon = goToBeginningIcon;
    // Screen readers will read "Go to the first video button"
    this.button_.setAttribute('aria-label', 'Go to the first video');
  } else if (this.isFirstManifest(nextManifest)){
    // Change icon and aria label back to the skip state
    this.button_.icon = skipIcon;
    this.button_.setAttribute('aria-label', 'Skip');
  }
});
```

#### Continue the Tutorials

Next, check out {@tutorial plugins}.



[Web Accessibility Standards]: https://www.w3.org/WAI/standards-guidelines/wcag/
[ARIA labels]: https://www.w3.org/TR/wai-aria/
[volume_up icon]: https://material.io/tools/icons/?icon=volume_up&style=baseline
[volume_off icon]: https://material.io/tools/icons/?icon=volume_off&style=baseline