/**
 * @license
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * @fileoverview Externs for XMLHttpRequest which are missing from the closure
 * compiler.
 *
 * @externs
 */


/** @type {number} */
XMLHttpRequest.UNSENT;


/** @type {number} */
XMLHttpRequest.OPENED;


/** @type {number} */
XMLHttpRequest.HEADERS_RECEIVED;


/** @type {number} */
XMLHttpRequest.LOADING;


/** @type {number} */
XMLHttpRequest.DONE;

