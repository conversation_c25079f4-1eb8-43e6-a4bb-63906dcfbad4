/**
 * @license
 * Copyright 2016 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


/**
 * @fileoverview Externs for MediaError properties not in the Closure compiler.
 *
 * @externs
 */


/**
 * A new field in the living standard to give a more informative diagnostic
 * message about the error.
 * @type {string}
 */
MediaError.prototype.message;


/**
 * A Microsoft Edge and IE extension to add a Windows error code.
 * @type {number}
 */
MediaError.prototype.msExtendedCode;
