{"name": "video-downloader-app", "version": "1.0.0", "description": "App para download de vídeos de qualquer site", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@expo/vector-icons": "14", "@react-native-async-storage/async-storage": "^2.2.0", "expo": "53.0.19", "expo-av": "15", "expo-file-system": "18", "expo-media-library": "17", "expo-status-bar": "2", "jest-expo": "53", "metro": "0.82.0", "metro-config": "0.82.0", "metro-resolver": "0.82.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-fs": "^2.20.0", "react-native-pager-view": "^6.8.1", "react-native-tab-view": "^4.1.2", "react-native-vector-icons": "^10.0.0", "react-native-video": "^5.2.1", "react-native-webview": "^13.15.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.0", "@testing-library/react-native": "^12.0.0", "jest": "^29.0.0", "jest-expo": "^49.0.0", "react-test-renderer": "^18.2.0"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}, "private": true}