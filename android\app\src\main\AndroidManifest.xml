<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.videodownloaderapp">

    <!-- Per<PERSON><PERSON>ões necessárias para o aplicativo -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- Per<PERSON>sões para Android 10+ (API 29+) -->
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" 
        android:maxSdkVersion="32" />

    <application
        android:name=".MainApplication"
        android:label="@string/app_name"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:allowBackup="false"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true">
        
        <activity
            android:name=".MainActivity"
            android:label="@string/app_name"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            
            <!-- Intent filters para capturar links de vídeo -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https" android:host="youtube.com" />
                <data android:scheme="https" android:host="www.youtube.com" />
                <data android:scheme="https" android:host="youtu.be" />
                <data android:scheme="https" android:host="vimeo.com" />
                <data android:scheme="https" android:host="www.vimeo.com" />
                <data android:scheme="https" android:host="tiktok.com" />
                <data android:scheme="https" android:host="www.tiktok.com" />
                <data android:scheme="https" android:host="instagram.com" />
                <data android:scheme="https" android:host="www.instagram.com" />
            </intent-filter>
        </activity>

        <!-- Provider para compartilhamento de arquivos -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- Service para downloads em background -->
        <service
            android:name=".DownloadService"
            android:enabled="true"
            android:exported="false" />
    </application>
</manifest>
