import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

const VideoQualitySelector = ({ 
  visible, 
  onClose, 
  videoUrl, 
  onQualitySelected,
  onDownloadStart 
}) => {
  const [qualities, setQualities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedQuality, setSelectedQuality] = useState(null);

  useEffect(() => {
    if (visible && videoUrl) {
      detectVideoQualities();
    }
  }, [visible, videoUrl]);

  const detectVideoQualities = async () => {
    setLoading(true);
    try {
      const detectedQualities = await analyzeVideoQualities(videoUrl);
      setQualities(detectedQualities);
      
      // Selecionar qualidade padrão (melhor disponível)
      if (detectedQualities.length > 0) {
        setSelectedQuality(detectedQualities[0]);
      }
    } catch (error) {
      console.error('Erro ao detectar qualidades:', error);
      Alert.alert('Erro', 'Não foi possível detectar as qualidades disponíveis');
    } finally {
      setLoading(false);
    }
  };

  const analyzeVideoQualities = async (url) => {
    try {
      // Tentar diferentes estratégias para detectar qualidades
      const strategies = [
        () => detectYouTubeQualities(url),
        () => detectVimeoQualities(url),
        () => detectGenericQualities(url),
        () => getDefaultQualities(url)
      ];

      for (const strategy of strategies) {
        try {
          const result = await strategy();
          if (result && result.length > 0) {
            return result;
          }
        } catch (e) {
          console.log('Estratégia falhou:', e.message);
          continue;
        }
      }

      // Fallback: qualidades padrão
      return getDefaultQualities(url);
    } catch (error) {
      console.error('Erro na análise de qualidades:', error);
      return getDefaultQualities(url);
    }
  };

  const detectYouTubeQualities = async (url) => {
    if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
      throw new Error('Não é YouTube');
    }

    try {
      // Extrair video ID
      let videoId = null;
      if (url.includes('youtube.com/watch')) {
        const urlObj = new URL(url);
        videoId = urlObj.searchParams.get('v');
      } else if (url.includes('youtu.be/')) {
        videoId = url.split('youtu.be/')[1].split('?')[0];
      }

      if (!videoId) {
        throw new Error('ID do vídeo não encontrado');
      }

      // Tentar obter informações reais do vídeo
      const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const html = await response.text();

      // Procurar por informações de qualidade no HTML
      const qualityPatterns = [
        /"qualityLabel":"([^"]*)".*?"url":"([^"]*)"/g,
        /"height":(\d+).*?"url":"([^"]*)"/g,
        /"quality":"([^"]*)".*?"url":"([^"]*)"/g
      ];

      const foundQualities = [];
      const qualityMap = new Map();

      for (const pattern of qualityPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let quality, videoUrl;

          if (match[1] && match[2]) {
            quality = match[1];
            videoUrl = match[2];
          }

          if (quality && videoUrl && !qualityMap.has(quality)) {
            qualityMap.set(quality, {
              quality: quality,
              label: quality,
              url: videoUrl.replace(/\\u0026/g, '&'),
              format: 'mp4',
              priority: getPriorityFromQuality(quality)
            });
          }
        }
      }

      if (qualityMap.size > 0) {
        return Array.from(qualityMap.values()).sort((a, b) => a.priority - b.priority);
      }

      // Fallback para qualidades padrão se não conseguir detectar
      return getStandardYouTubeQualities(url);

    } catch (error) {
      console.log('Erro na detecção do YouTube:', error);
      return getStandardYouTubeQualities(url);
    }
  };

  const getStandardYouTubeQualities = (url) => {
    return [
      {
        quality: '2160p',
        label: '2160p 4K',
        size: 'Estimado: ~500MB',
        url: url,
        format: 'mp4',
        priority: 1
      },
      {
        quality: '1440p',
        label: '1440p QHD',
        size: 'Estimado: ~300MB',
        url: url,
        format: 'mp4',
        priority: 2
      },
      {
        quality: '1080p',
        label: '1080p Full HD',
        size: 'Estimado: ~150MB',
        url: url,
        format: 'mp4',
        priority: 3
      },
      {
        quality: '720p',
        label: '720p HD',
        size: 'Estimado: ~80MB',
        url: url,
        format: 'mp4',
        priority: 4
      },
      {
        quality: '480p',
        label: '480p',
        size: 'Estimado: ~40MB',
        url: url,
        format: 'mp4',
        priority: 5
      },
      {
        quality: '360p',
        label: '360p',
        size: 'Estimado: ~20MB',
        url: url,
        format: 'mp4',
        priority: 6
      },
      {
        quality: '240p',
        label: '240p',
        size: 'Estimado: ~10MB',
        url: url,
        format: 'mp4',
        priority: 7
      }
    ];
  };

  const getPriorityFromQuality = (quality) => {
    const qualityOrder = {
      '2160p': 1, '4K': 1,
      '1440p': 2, 'QHD': 2,
      '1080p': 3, 'Full HD': 3,
      '720p': 4, 'HD': 4,
      '480p': 5,
      '360p': 6,
      '240p': 7,
      '144p': 8
    };

    for (const [key, priority] of Object.entries(qualityOrder)) {
      if (quality.includes(key)) {
        return priority;
      }
    }

    return 9; // Qualidade desconhecida
  };

  const detectVimeoQualities = async (url) => {
    if (!url.includes('vimeo.com')) {
      throw new Error('Não é Vimeo');
    }

    try {
      // Extrair video ID do Vimeo
      const videoId = url.match(/vimeo\.com\/(\d+)/)?.[1];
      if (!videoId) {
        throw new Error('ID do vídeo Vimeo não encontrado');
      }

      // Tentar obter informações do vídeo via API pública do Vimeo
      const response = await fetch(`https://vimeo.com/${videoId}`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const html = await response.text();

      // Procurar por configurações de qualidade no HTML
      const configMatch = html.match(/"config_url":"([^"]*)"/);
      if (configMatch) {
        try {
          const configUrl = configMatch[1].replace(/\\u002F/g, '/');
          const configResponse = await fetch(configUrl);
          const config = await configResponse.json();

          if (config.request && config.request.files && config.request.files.progressive) {
            const qualities = config.request.files.progressive.map(file => ({
              quality: `${file.height}p`,
              label: `${file.height}p ${file.height >= 1080 ? 'Full HD' : file.height >= 720 ? 'HD' : ''}`.trim(),
              size: `${(file.size / 1024 / 1024).toFixed(1)} MB`,
              url: file.url,
              format: 'mp4',
              priority: getPriorityFromQuality(`${file.height}p`)
            }));

            return qualities.sort((a, b) => a.priority - b.priority);
          }
        } catch (e) {
          console.log('Erro ao obter config do Vimeo:', e);
        }
      }

      // Fallback para qualidades padrão
      return getStandardVimeoQualities(url);

    } catch (error) {
      console.log('Erro na detecção do Vimeo:', error);
      return getStandardVimeoQualities(url);
    }
  };

  const getStandardVimeoQualities = (url) => {
    return [
      {
        quality: '1080p',
        label: '1080p Full HD',
        size: 'Estimado: ~150MB',
        url: url,
        format: 'mp4',
        priority: 1
      },
      {
        quality: '720p',
        label: '720p HD',
        size: 'Estimado: ~80MB',
        url: url,
        format: 'mp4',
        priority: 2
      },
      {
        quality: '540p',
        label: '540p',
        size: 'Estimado: ~50MB',
        url: url,
        format: 'mp4',
        priority: 3
      },
      {
        quality: '360p',
        label: '360p',
        size: 'Estimado: ~25MB',
        url: url,
        format: 'mp4',
        priority: 4
      }
    ];
  };

  const detectGenericQualities = async (url) => {
    try {
      // Primeiro, tentar detectar se é um site conhecido com múltiplas qualidades
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const html = await response.text();

      // Procurar por múltiplas qualidades no HTML
      const qualityPatterns = [
        // Padrões para sites de vídeo genéricos
        /"(\d+p)"[^>]*"([^"]*\.mp4[^"]*)"/g,
        /"quality[^"]*"[^"]*"(\d+p?)"[^>]*"url[^"]*"[^"]*"([^"]*)"/g,
        /"(\d+)"[^>]*"height"[^>]*"([^"]*\.mp4[^"]*)"/g,
        /data-quality="(\d+p?)"[^>]*data-src="([^"]*)"/g,
        /quality[_-](\d+p?)[^>]*src[^"]*"([^"]*)"/g
      ];

      const foundQualities = new Map();

      for (const pattern of qualityPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let quality = match[1];
          let videoUrl = match[2];

          // Normalizar qualidade
          if (!quality.includes('p')) {
            quality = quality + 'p';
          }

          // Verificar se é uma URL válida
          if (videoUrl && (videoUrl.startsWith('http') || videoUrl.startsWith('//'))) {
            if (videoUrl.startsWith('//')) {
              videoUrl = 'https:' + videoUrl;
            }

            foundQualities.set(quality, {
              quality: quality,
              label: `${quality} ${getQualityLabel(quality)}`,
              size: 'Tamanho variável',
              url: videoUrl,
              format: getFormatFromUrl(videoUrl),
              priority: getPriorityFromQuality(quality)
            });
          }
        }
      }

      if (foundQualities.size > 0) {
        return Array.from(foundQualities.values()).sort((a, b) => a.priority - b.priority);
      }

      // Se não encontrou múltiplas qualidades, verificar se é um vídeo direto
      const headResponse = await fetch(url, { method: 'HEAD' });
      const contentLength = headResponse.headers.get('content-length');
      const contentType = headResponse.headers.get('content-type');

      if (!contentType || !contentType.includes('video')) {
        throw new Error('Não é um vídeo direto');
      }

      const sizeInMB = contentLength ? (parseInt(contentLength) / 1024 / 1024).toFixed(1) : 'Desconhecido';

      return [
        {
          quality: 'Original',
          label: 'Qualidade Original',
          size: `${sizeInMB} MB`,
          url: url,
          format: getFormatFromUrl(url),
          priority: 1
        }
      ];
    } catch (error) {
      throw new Error('Falha na detecção genérica');
    }
  };

  const getQualityLabel = (quality) => {
    const labels = {
      '2160p': '4K',
      '1440p': 'QHD',
      '1080p': 'Full HD',
      '720p': 'HD',
      '480p': 'SD',
      '360p': 'SD',
      '240p': 'SD',
      '144p': 'SD'
    };

    return labels[quality] || '';
  };

  const getDefaultQualities = (url) => {
    // Tentar detectar o tipo de site para oferecer qualidades mais específicas
    const hostname = new URL(url).hostname.toLowerCase();

    if (hostname.includes('instagram')) {
      return [
        {
          quality: '720p',
          label: '720p HD',
          size: 'Estimado: ~50MB',
          url: url,
          format: 'mp4',
          priority: 1
        },
        {
          quality: '480p',
          label: '480p',
          size: 'Estimado: ~25MB',
          url: url,
          format: 'mp4',
          priority: 2
        }
      ];
    }

    if (hostname.includes('tiktok')) {
      return [
        {
          quality: '720p',
          label: '720p HD',
          size: 'Estimado: ~30MB',
          url: url,
          format: 'mp4',
          priority: 1
        },
        {
          quality: '480p',
          label: '480p',
          size: 'Estimado: ~15MB',
          url: url,
          format: 'mp4',
          priority: 2
        }
      ];
    }

    if (hostname.includes('facebook') || hostname.includes('fb.')) {
      return [
        {
          quality: '1080p',
          label: '1080p Full HD',
          size: 'Estimado: ~100MB',
          url: url,
          format: 'mp4',
          priority: 1
        },
        {
          quality: '720p',
          label: '720p HD',
          size: 'Estimado: ~60MB',
          url: url,
          format: 'mp4',
          priority: 2
        },
        {
          quality: '480p',
          label: '480p',
          size: 'Estimado: ~30MB',
          url: url,
          format: 'mp4',
          priority: 3
        }
      ];
    }

    // Qualidades padrão para sites desconhecidos
    return [
      {
        quality: '1080p',
        label: '1080p Full HD',
        size: 'Estimado: ~120MB',
        url: url,
        format: getFormatFromUrl(url),
        priority: 1
      },
      {
        quality: '720p',
        label: '720p HD',
        size: 'Estimado: ~70MB',
        url: url,
        format: getFormatFromUrl(url),
        priority: 2
      },
      {
        quality: '480p',
        label: '480p',
        size: 'Estimado: ~35MB',
        url: url,
        format: getFormatFromUrl(url),
        priority: 3
      },
      {
        quality: '360p',
        label: '360p',
        size: 'Estimado: ~20MB',
        url: url,
        format: getFormatFromUrl(url),
        priority: 4
      },
      {
        quality: 'Auto',
        label: 'Qualidade Automática',
        size: 'Melhor disponível',
        url: url,
        format: getFormatFromUrl(url),
        priority: 5
      }
    ];
  };

  const getFormatFromUrl = (url) => {
    if (url.includes('.mp4')) return 'mp4';
    if (url.includes('.webm')) return 'webm';
    if (url.includes('.avi')) return 'avi';
    if (url.includes('.mov')) return 'mov';
    return 'mp4'; // padrão
  };

  const handleDownload = () => {
    if (!selectedQuality) {
      Alert.alert('Erro', 'Selecione uma qualidade primeiro');
      return;
    }

    onQualitySelected && onQualitySelected(selectedQuality);
    onDownloadStart && onDownloadStart(selectedQuality);
    onClose();
  };

  const getQualityIcon = (quality) => {
    const q = quality.quality.toLowerCase();

    if (q.includes('2160') || q.includes('4k')) {
      return 'diamond';
    } else if (q.includes('1440') || q.includes('qhd')) {
      return 'star';
    } else if (q.includes('1080') || q.includes('full hd')) {
      return 'videocam';
    } else if (q.includes('720') || q.includes('hd')) {
      return 'videocam-outline';
    } else if (q.includes('480')) {
      return 'tablet-portrait';
    } else if (q.includes('360')) {
      return 'phone-portrait';
    } else if (q.includes('240') || q.includes('144')) {
      return 'phone-portrait-outline';
    } else if (q.includes('auto') || q.includes('original')) {
      return 'settings';
    } else {
      return 'play';
    }
  };

  const getQualityColor = (quality) => {
    const q = quality.quality.toLowerCase();

    if (q.includes('2160') || q.includes('4k')) {
      return '#E91E63'; // Rosa para 4K
    } else if (q.includes('1440') || q.includes('qhd')) {
      return '#9C27B0'; // Roxo para QHD
    } else if (q.includes('1080') || q.includes('full hd')) {
      return '#4CAF50'; // Verde para Full HD
    } else if (q.includes('720') || q.includes('hd')) {
      return '#2196F3'; // Azul para HD
    } else if (q.includes('480')) {
      return '#FF9800'; // Laranja para 480p
    } else if (q.includes('360')) {
      return '#FF5722'; // Vermelho-laranja para 360p
    } else if (q.includes('240') || q.includes('144')) {
      return '#F44336'; // Vermelho para baixa qualidade
    } else if (q.includes('auto') || q.includes('original')) {
      return '#6c5ce7'; // Roxo padrão para auto
    } else {
      return '#607D8B'; // Cinza para desconhecido
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>Selecionar Qualidade</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#6c5ce7" />
              <Text style={styles.loadingText}>Analisando qualidades disponíveis...</Text>
            </View>
          ) : (
            <>
              <ScrollView style={styles.qualitiesList}>
                {qualities.map((quality, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.qualityItem,
                      selectedQuality?.quality === quality.quality && styles.selectedQuality
                    ]}
                    onPress={() => setSelectedQuality(quality)}
                  >
                    <View style={styles.qualityIcon}>
                      <Ionicons 
                        name={getQualityIcon(quality)} 
                        size={24} 
                        color={getQualityColor(quality)} 
                      />
                    </View>
                    
                    <View style={styles.qualityInfo}>
                      <Text style={styles.qualityLabel}>{quality.label}</Text>
                      <Text style={styles.qualityDetails}>
                        {quality.size} • {quality.format.toUpperCase()}
                      </Text>
                    </View>
                    
                    <View style={styles.qualityBadge}>
                      <Text style={[styles.qualityBadgeText, { color: getQualityColor(quality) }]}>
                        {quality.quality}
                      </Text>
                    </View>
                    
                    {selectedQuality?.quality === quality.quality && (
                      <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                    )}
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <View style={styles.footer}>
                <TouchableOpacity 
                  style={styles.cancelButton}
                  onPress={onClose}
                >
                  <Text style={styles.cancelButtonText}>Cancelar</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.downloadButton, !selectedQuality && styles.downloadButtonDisabled]}
                  onPress={handleDownload}
                  disabled={!selectedQuality}
                >
                  <Ionicons name="download" size={20} color="#fff" />
                  <Text style={styles.downloadButtonText}>Baixar</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    width: width * 0.9,
    maxHeight: height * 0.8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 15,
    textAlign: 'center',
  },
  qualitiesList: {
    maxHeight: height * 0.5,
  },
  qualityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  selectedQuality: {
    backgroundColor: '#2d2d44',
  },
  qualityIcon: {
    marginRight: 15,
  },
  qualityInfo: {
    flex: 1,
  },
  qualityLabel: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  qualityDetails: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  qualityBadge: {
    marginRight: 10,
  },
  qualityBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#666',
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  downloadButton: {
    flex: 1,
    flexDirection: 'row',
    padding: 15,
    borderRadius: 8,
    backgroundColor: '#6c5ce7',
    marginLeft: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  downloadButtonDisabled: {
    backgroundColor: '#666',
  },
  downloadButtonText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 5,
  },
});

export default VideoQualitySelector;
