import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

const VideoQualitySelector = ({ 
  visible, 
  onClose, 
  videoUrl, 
  onQualitySelected,
  onDownloadStart 
}) => {
  const [qualities, setQualities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedQuality, setSelectedQuality] = useState(null);

  useEffect(() => {
    if (visible && videoUrl) {
      detectVideoQualities();
    }
  }, [visible, videoUrl]);

  const detectVideoQualities = async () => {
    setLoading(true);
    try {
      const detectedQualities = await analyzeVideoQualities(videoUrl);
      setQualities(detectedQualities);
      
      // Selecionar qualidade padrão (melhor disponível)
      if (detectedQualities.length > 0) {
        setSelectedQuality(detectedQualities[0]);
      }
    } catch (error) {
      console.error('Erro ao detectar qualidades:', error);
      Alert.alert('Erro', 'Não foi possível detectar as qualidades disponíveis');
    } finally {
      setLoading(false);
    }
  };

  const analyzeVideoQualities = async (url) => {
    try {
      // Tentar diferentes estratégias para detectar qualidades
      const strategies = [
        () => detectYouTubeQualities(url),
        () => detectVimeoQualities(url),
        () => detectGenericQualities(url),
        () => getDefaultQualities(url)
      ];

      for (const strategy of strategies) {
        try {
          const result = await strategy();
          if (result && result.length > 0) {
            return result;
          }
        } catch (e) {
          console.log('Estratégia falhou:', e.message);
          continue;
        }
      }

      // Fallback: qualidades padrão
      return getDefaultQualities(url);
    } catch (error) {
      console.error('Erro na análise de qualidades:', error);
      return getDefaultQualities(url);
    }
  };

  const detectYouTubeQualities = async (url) => {
    if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
      throw new Error('Não é YouTube');
    }

    // Simular detecção de qualidades do YouTube
    return [
      {
        quality: '1080p',
        label: '1080p HD',
        size: 'Estimado: ~100MB',
        url: url,
        format: 'mp4',
        priority: 1
      },
      {
        quality: '720p',
        label: '720p HD',
        size: 'Estimado: ~60MB',
        url: url,
        format: 'mp4',
        priority: 2
      },
      {
        quality: '480p',
        label: '480p',
        size: 'Estimado: ~30MB',
        url: url,
        format: 'mp4',
        priority: 3
      },
      {
        quality: '360p',
        label: '360p',
        size: 'Estimado: ~15MB',
        url: url,
        format: 'mp4',
        priority: 4
      }
    ];
  };

  const detectVimeoQualities = async (url) => {
    if (!url.includes('vimeo.com')) {
      throw new Error('Não é Vimeo');
    }

    return [
      {
        quality: '1080p',
        label: '1080p Full HD',
        size: 'Estimado: ~120MB',
        url: url,
        format: 'mp4',
        priority: 1
      },
      {
        quality: '720p',
        label: '720p HD',
        size: 'Estimado: ~70MB',
        url: url,
        format: 'mp4',
        priority: 2
      },
      {
        quality: '540p',
        label: '540p',
        size: 'Estimado: ~40MB',
        url: url,
        format: 'mp4',
        priority: 3
      }
    ];
  };

  const detectGenericQualities = async (url) => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      const contentType = response.headers.get('content-type');

      if (!contentType || !contentType.includes('video')) {
        throw new Error('Não é um vídeo direto');
      }

      const sizeInMB = contentLength ? (parseInt(contentLength) / 1024 / 1024).toFixed(1) : 'Desconhecido';
      
      return [
        {
          quality: 'Original',
          label: 'Qualidade Original',
          size: `${sizeInMB} MB`,
          url: url,
          format: getFormatFromUrl(url),
          priority: 1
        }
      ];
    } catch (error) {
      throw new Error('Falha na detecção genérica');
    }
  };

  const getDefaultQualities = (url) => {
    return [
      {
        quality: 'Auto',
        label: 'Qualidade Automática',
        size: 'Tamanho variável',
        url: url,
        format: getFormatFromUrl(url),
        priority: 1
      },
      {
        quality: 'Best',
        label: 'Melhor Qualidade Disponível',
        size: 'Máximo disponível',
        url: url,
        format: getFormatFromUrl(url),
        priority: 2
      },
      {
        quality: 'Medium',
        label: 'Qualidade Média',
        size: 'Balanceado',
        url: url,
        format: getFormatFromUrl(url),
        priority: 3
      }
    ];
  };

  const getFormatFromUrl = (url) => {
    if (url.includes('.mp4')) return 'mp4';
    if (url.includes('.webm')) return 'webm';
    if (url.includes('.avi')) return 'avi';
    if (url.includes('.mov')) return 'mov';
    return 'mp4'; // padrão
  };

  const handleDownload = () => {
    if (!selectedQuality) {
      Alert.alert('Erro', 'Selecione uma qualidade primeiro');
      return;
    }

    onQualitySelected && onQualitySelected(selectedQuality);
    onDownloadStart && onDownloadStart(selectedQuality);
    onClose();
  };

  const getQualityIcon = (quality) => {
    switch (quality.quality) {
      case '1080p':
        return 'videocam';
      case '720p':
        return 'videocam-outline';
      case '480p':
      case '360p':
        return 'phone-portrait';
      default:
        return 'play';
    }
  };

  const getQualityColor = (quality) => {
    switch (quality.quality) {
      case '1080p':
        return '#4CAF50';
      case '720p':
        return '#2196F3';
      case '480p':
        return '#FF9800';
      case '360p':
        return '#F44336';
      default:
        return '#6c5ce7';
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>Selecionar Qualidade</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#6c5ce7" />
              <Text style={styles.loadingText}>Analisando qualidades disponíveis...</Text>
            </View>
          ) : (
            <>
              <ScrollView style={styles.qualitiesList}>
                {qualities.map((quality, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      styles.qualityItem,
                      selectedQuality?.quality === quality.quality && styles.selectedQuality
                    ]}
                    onPress={() => setSelectedQuality(quality)}
                  >
                    <View style={styles.qualityIcon}>
                      <Ionicons 
                        name={getQualityIcon(quality)} 
                        size={24} 
                        color={getQualityColor(quality)} 
                      />
                    </View>
                    
                    <View style={styles.qualityInfo}>
                      <Text style={styles.qualityLabel}>{quality.label}</Text>
                      <Text style={styles.qualityDetails}>
                        {quality.size} • {quality.format.toUpperCase()}
                      </Text>
                    </View>
                    
                    <View style={styles.qualityBadge}>
                      <Text style={[styles.qualityBadgeText, { color: getQualityColor(quality) }]}>
                        {quality.quality}
                      </Text>
                    </View>
                    
                    {selectedQuality?.quality === quality.quality && (
                      <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                    )}
                  </TouchableOpacity>
                ))}
              </ScrollView>

              <View style={styles.footer}>
                <TouchableOpacity 
                  style={styles.cancelButton}
                  onPress={onClose}
                >
                  <Text style={styles.cancelButtonText}>Cancelar</Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[styles.downloadButton, !selectedQuality && styles.downloadButtonDisabled]}
                  onPress={handleDownload}
                  disabled={!selectedQuality}
                >
                  <Ionicons name="download" size={20} color="#fff" />
                  <Text style={styles.downloadButtonText}>Baixar</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    width: width * 0.9,
    maxHeight: height * 0.8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  title: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    marginTop: 15,
    textAlign: 'center',
  },
  qualitiesList: {
    maxHeight: height * 0.5,
  },
  qualityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  selectedQuality: {
    backgroundColor: '#2d2d44',
  },
  qualityIcon: {
    marginRight: 15,
  },
  qualityInfo: {
    flex: 1,
  },
  qualityLabel: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  qualityDetails: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  qualityBadge: {
    marginRight: 10,
  },
  qualityBadgeText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#666',
    marginRight: 10,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  downloadButton: {
    flex: 1,
    flexDirection: 'row',
    padding: 15,
    borderRadius: 8,
    backgroundColor: '#6c5ce7',
    marginLeft: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  downloadButtonDisabled: {
    backgroundColor: '#666',
  },
  downloadButtonText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 5,
  },
});

export default VideoQualitySelector;
