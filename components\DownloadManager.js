import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Dimensions,
  Modal
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';

const { width } = Dimensions.get('window');

const DownloadManager = ({ visible, onClose }) => {
  const [downloads, setDownloads] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDownload, setSelectedDownload] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    if (visible) {
      loadDownloads();
    }
  }, [visible]);

  const loadDownloads = async () => {
    try {
      const savedDownloads = await AsyncStorage.getItem('video_downloads');
      if (savedDownloads) {
        const parsedDownloads = JSON.parse(savedDownloads);
        // Verificar se os arquivos ainda existem
        const validDownloads = await Promise.all(
          parsedDownloads.map(async (download) => {
            try {
              const fileInfo = await FileSystem.getInfoAsync(download.uri);
              return {
                ...download,
                exists: fileInfo.exists,
                actualSize: fileInfo.exists ? fileInfo.size : 0
              };
            } catch {
              return { ...download, exists: false, actualSize: 0 };
            }
          })
        );
        setDownloads(validDownloads.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)));
      }
    } catch (error) {
      console.error('Erro ao carregar downloads:', error);
    }
  };

  const saveDownloads = async (newDownloads) => {
    try {
      await AsyncStorage.setItem('video_downloads', JSON.stringify(newDownloads));
    } catch (error) {
      console.error('Erro ao salvar downloads:', error);
    }
  };

  const addDownload = async (downloadInfo) => {
    const newDownload = {
      id: Date.now().toString(),
      ...downloadInfo,
      timestamp: new Date().toISOString(),
      status: 'completed'
    };
    
    const updatedDownloads = [newDownload, ...downloads];
    setDownloads(updatedDownloads);
    await saveDownloads(updatedDownloads);
  };

  const deleteDownload = async (downloadId) => {
    Alert.alert(
      'Confirmar Exclusão',
      'Deseja excluir este download? O arquivo será removido permanentemente.',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: async () => {
            try {
              const download = downloads.find(d => d.id === downloadId);
              if (download && download.uri) {
                // Remover arquivo
                await FileSystem.deleteAsync(download.uri, { idempotent: true });
              }
              
              // Remover da lista
              const updatedDownloads = downloads.filter(d => d.id !== downloadId);
              setDownloads(updatedDownloads);
              await saveDownloads(updatedDownloads);
              
              Alert.alert('Sucesso', 'Download excluído com sucesso');
            } catch (error) {
              console.error('Erro ao excluir download:', error);
              Alert.alert('Erro', 'Não foi possível excluir o download');
            }
          }
        }
      ]
    );
  };

  const shareDownload = async (download) => {
    try {
      // Implementar compartilhamento
      Alert.alert('Compartilhar', 'Funcionalidade de compartilhamento será implementada');
    } catch (error) {
      Alert.alert('Erro', 'Não foi possível compartilhar o arquivo');
    }
  };

  const playDownload = async (download) => {
    try {
      // Implementar reprodução
      Alert.alert('Reproduzir', 'Funcionalidade de reprodução será implementada');
    } catch (error) {
      Alert.alert('Erro', 'Não foi possível reproduzir o arquivo');
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR') + ' ' + date.toLocaleTimeString('pt-BR', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getStatusIcon = (download) => {
    if (!download.exists) return { name: 'alert-circle', color: '#ff4444' };
    return { name: 'checkmark-circle', color: '#4CAF50' };
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDownloads();
    setRefreshing(false);
  };

  const clearAllDownloads = () => {
    Alert.alert(
      'Limpar Todos os Downloads',
      'Deseja remover todos os downloads da lista? Os arquivos não serão excluídos.',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Limpar',
          style: 'destructive',
          onPress: async () => {
            setDownloads([]);
            await saveDownloads([]);
          }
        }
      ]
    );
  };

  const showDownloadDetails = (download) => {
    setSelectedDownload(download);
    setShowDetails(true);
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.title}>Downloads</Text>
          <TouchableOpacity onPress={clearAllDownloads}>
            <Ionicons name="trash-outline" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        <View style={styles.stats}>
          <Text style={styles.statsText}>
            {downloads.length} downloads • {downloads.filter(d => d.exists).length} disponíveis
          </Text>
        </View>

        <ScrollView 
          style={styles.downloadsList}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          {downloads.map((download) => {
            const statusIcon = getStatusIcon(download);
            
            return (
              <View key={download.id} style={styles.downloadItem}>
                <TouchableOpacity 
                  style={styles.downloadContent}
                  onPress={() => showDownloadDetails(download)}
                >
                  <View style={styles.downloadIcon}>
                    <Ionicons name="videocam" size={24} color="#6c5ce7" />
                    <View style={styles.statusIndicator}>
                      <Ionicons 
                        name={statusIcon.name} 
                        size={12} 
                        color={statusIcon.color} 
                      />
                    </View>
                  </View>
                  
                  <View style={styles.downloadInfo}>
                    <Text style={styles.downloadTitle} numberOfLines={2}>
                      {download.fileName || 'Video Download'}
                    </Text>
                    <Text style={styles.downloadUrl} numberOfLines={1}>
                      {download.originalUrl}
                    </Text>
                    <View style={styles.downloadMeta}>
                      <Text style={styles.downloadSize}>
                        {formatFileSize(download.actualSize || download.size || 0)}
                      </Text>
                      <Text style={styles.downloadDate}>
                        {formatDate(download.timestamp)}
                      </Text>
                    </View>
                    {download.quality && (
                      <Text style={styles.downloadQuality}>
                        Qualidade: {download.quality.label || download.quality.quality}
                      </Text>
                    )}
                  </View>
                </TouchableOpacity>
                
                <View style={styles.downloadActions}>
                  {download.exists && (
                    <TouchableOpacity 
                      style={styles.actionButton}
                      onPress={() => playDownload(download)}
                    >
                      <Ionicons name="play" size={20} color="#4CAF50" />
                    </TouchableOpacity>
                  )}
                  
                  {download.exists && (
                    <TouchableOpacity 
                      style={styles.actionButton}
                      onPress={() => shareDownload(download)}
                    >
                      <Ionicons name="share" size={20} color="#2196F3" />
                    </TouchableOpacity>
                  )}
                  
                  <TouchableOpacity 
                    style={styles.actionButton}
                    onPress={() => deleteDownload(download.id)}
                  >
                    <Ionicons name="trash" size={20} color="#ff4444" />
                  </TouchableOpacity>
                </View>
              </View>
            );
          })}
          
          {downloads.length === 0 && (
            <View style={styles.emptyState}>
              <Ionicons name="download-outline" size={64} color="#666" />
              <Text style={styles.emptyTitle}>Nenhum download</Text>
              <Text style={styles.emptyMessage}>
                Seus downloads de vídeo aparecerão aqui
              </Text>
            </View>
          )}
        </ScrollView>

        {/* Modal de detalhes */}
        <Modal
          visible={showDetails}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDetails(false)}
        >
          <View style={styles.detailsOverlay}>
            <View style={styles.detailsContainer}>
              <View style={styles.detailsHeader}>
                <Text style={styles.detailsTitle}>Detalhes do Download</Text>
                <TouchableOpacity onPress={() => setShowDetails(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              {selectedDownload && (
                <ScrollView style={styles.detailsContent}>
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Nome do Arquivo:</Text>
                    <Text style={styles.detailValue}>{selectedDownload.fileName}</Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>URL Original:</Text>
                    <Text style={styles.detailValue}>{selectedDownload.originalUrl}</Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Tamanho:</Text>
                    <Text style={styles.detailValue}>
                      {formatFileSize(selectedDownload.actualSize || selectedDownload.size || 0)}
                    </Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Data do Download:</Text>
                    <Text style={styles.detailValue}>
                      {formatDate(selectedDownload.timestamp)}
                    </Text>
                  </View>
                  
                  {selectedDownload.quality && (
                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>Qualidade:</Text>
                      <Text style={styles.detailValue}>
                        {selectedDownload.quality.label || selectedDownload.quality.quality}
                      </Text>
                    </View>
                  )}
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Status:</Text>
                    <Text style={[
                      styles.detailValue,
                      { color: selectedDownload.exists ? '#4CAF50' : '#ff4444' }
                    ]}>
                      {selectedDownload.exists ? 'Arquivo disponível' : 'Arquivo não encontrado'}
                    </Text>
                  </View>
                </ScrollView>
              )}
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

// Função para ser usada externamente para adicionar downloads
DownloadManager.addDownload = async (downloadInfo) => {
  try {
    const savedDownloads = await AsyncStorage.getItem('video_downloads');
    const downloads = savedDownloads ? JSON.parse(savedDownloads) : [];
    
    const newDownload = {
      id: Date.now().toString(),
      ...downloadInfo,
      timestamp: new Date().toISOString(),
      status: 'completed'
    };
    
    const updatedDownloads = [newDownload, ...downloads];
    await AsyncStorage.setItem('video_downloads', JSON.stringify(updatedDownloads));
  } catch (error) {
    console.error('Erro ao adicionar download:', error);
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 50,
    backgroundColor: '#1a1a2e',
  },
  title: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  stats: {
    padding: 15,
    backgroundColor: '#2d2d44',
    marginHorizontal: 20,
    borderRadius: 10,
    marginBottom: 20,
  },
  statsText: {
    color: '#fff',
    textAlign: 'center',
  },
  downloadsList: {
    flex: 1,
    paddingHorizontal: 20,
  },
  downloadItem: {
    flexDirection: 'row',
    backgroundColor: '#2d2d44',
    borderRadius: 10,
    marginBottom: 10,
    overflow: 'hidden',
  },
  downloadContent: {
    flex: 1,
    flexDirection: 'row',
    padding: 15,
  },
  downloadIcon: {
    position: 'relative',
    marginRight: 15,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#1a1a2e',
    borderRadius: 8,
    padding: 2,
  },
  downloadInfo: {
    flex: 1,
  },
  downloadTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  downloadUrl: {
    color: '#888',
    fontSize: 12,
    marginBottom: 5,
  },
  downloadMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  downloadSize: {
    color: '#6c5ce7',
    fontSize: 12,
    fontWeight: 'bold',
  },
  downloadDate: {
    color: '#888',
    fontSize: 12,
  },
  downloadQuality: {
    color: '#4CAF50',
    fontSize: 12,
  },
  downloadActions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 10,
  },
  actionButton: {
    padding: 10,
    marginLeft: 5,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
  },
  emptyMessage: {
    color: '#888',
    textAlign: 'center',
    marginTop: 10,
  },
  detailsOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailsContainer: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    width: width * 0.9,
    maxHeight: '80%',
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  detailsTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  detailsContent: {
    padding: 20,
  },
  detailItem: {
    marginBottom: 15,
  },
  detailLabel: {
    color: '#888',
    fontSize: 14,
    marginBottom: 5,
  },
  detailValue: {
    color: '#fff',
    fontSize: 16,
  },
});

export default DownloadManager;
