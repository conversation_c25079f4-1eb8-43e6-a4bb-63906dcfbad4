{"version": 3, "file": "TabBar.d.ts", "sourceRoot": "", "sources": ["../../../src/TabBar.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAQL,KAAK,4BAA4B,EACjC,KAAK,SAAS,EAGd,KAAK,SAAS,EAEf,MAAM,cAAc,CAAC;AAGtB,OAAO,EACL,KAAK,KAAK,IAAI,cAAc,EAE7B,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,KAAK,KAAK,IAAI,eAAe,EAAc,MAAM,cAAc,CAAC;AACzE,OAAO,KAAK,EACV,KAAK,EAEL,eAAe,EACf,eAAe,EACf,KAAK,EACL,KAAK,EACL,kBAAkB,EAClB,aAAa,EACd,MAAM,SAAS,CAAC;AAGjB,MAAM,MAAM,KAAK,CAAC,CAAC,SAAS,KAAK,IAAI,kBAAkB,GAAG;IACxD,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IACpC,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C,eAAe,CAAC,EAAE,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC;IAChE,gBAAgB,CAAC,EAAE,CACjB,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,GAAG;QAAE,GAAG,EAAE,MAAM,CAAA;KAAE,KACxC,KAAK,CAAC,YAAY,CAAC;IACxB,UAAU,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC;IAC/C,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;IAC3C,QAAQ,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAChC,cAAc,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IACtC,uBAAuB,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC/C,qBAAqB,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC7C,KAAK,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC7B,SAAS,CAAC,EAAE,eAAe,CAAC;IAC5B,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,cAAc,CAAC,EAAE,4BAA4B,CAAC;CAC/C,CAAC;AAkRF,wBAAgB,MAAM,CAAC,CAAC,SAAS,KAAK,EAAE,EACtC,eAAwC,EACxC,GAAO,EACP,aAAa,EACb,MAAM,EACN,eAAe,EACf,QAAQ,EACR,WAAW,EACX,OAAO,EACP,qBAAqB,EACrB,aAAa,EACb,uBAAuB,EACvB,cAAc,EACd,cAAc,EACd,UAAU,EACV,UAAU,EACV,YAAY,EACZ,SAA4D,EAC5D,gBAAgB,EAChB,KAAK,EACL,QAAQ,EACR,MAAM,EAAE,UAAU,EAClB,MAAM,EACN,cAAc,EACd,OAAO,GACR,EAAE,KAAK,CAAC,CAAC,CAAC,2CA4UV"}