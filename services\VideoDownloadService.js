import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';

class VideoDownloadService {
  constructor() {
    // Removido: lista de sites permitidos - agora aceita qualquer URL
  }

  // Detectar tipo de site e extrair informações do vídeo
  async analyzeUrl(url) {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase().replace('www.', '');

      // Aceitar qualquer URL válida - sem restrições de site
      return {
        hostname,
        isSupported: true, // Sempre true - aceita qualquer site
        originalUrl: url,
        cleanUrl: this.cleanUrl(url)
      };
    } catch (error) {
      throw new Error('URL inválida');
    }
  }

  // Limpar URL removendo parâmetros desnecessários
  cleanUrl(url) {
    try {
      const urlObj = new URL(url);
      
      // Para YouTube, manter apenas o parâmetro 'v'
      if (urlObj.hostname.includes('youtube.com')) {
        const videoId = urlObj.searchParams.get('v');
        if (videoId) {
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }
      
      // Para youtu.be, converter para formato padrão
      if (urlObj.hostname.includes('youtu.be')) {
        const videoId = urlObj.pathname.slice(1);
        return `https://www.youtube.com/watch?v=${videoId}`;
      }

      return url;
    } catch {
      return url;
    }
  }

  // Tentar extrair URLs diretas de vídeo de qualquer site
  async extractDirectVideoUrl(url) {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      const html = await response.text();
      const foundUrls = new Set(); // Usar Set para evitar duplicatas

      // Padrões mais abrangentes para encontrar vídeos em qualquer site
      const videoPatterns = [
        // URLs diretas de vídeo com alta prioridade
        /"url":"([^"]*\.mp4[^"]*)"/g,
        /"url":"([^"]*\.webm[^"]*)"/g,
        /"src":"([^"]*\.mp4[^"]*)"/g,
        /"src":"([^"]*\.webm[^"]*)"/g,
        /"file":"([^"]*\.mp4[^"]*)"/g,
        /"videoUrl":"([^"]*)"/g,
        /"video_url":"([^"]*)"/g,
        /"contentUrl":"([^"]*)"/g,

        // Padrões para tags HTML
        /src="([^"]*\.mp4[^"]*)"/g,
        /src="([^"]*\.webm[^"]*)"/g,
        /href="([^"]*\.mp4[^"]*)"/g,
        /href="([^"]*\.webm[^"]*)"/g,

        // URLs completas
        /"(https?:\/\/[^"]*\.mp4[^"]*)"/g,
        /"(https?:\/\/[^"]*\.webm[^"]*)"/g,
        /"(https?:\/\/[^"]*\.avi[^"]*)"/g,
        /"(https?:\/\/[^"]*\.mov[^"]*)"/g,
        /"(https?:\/\/[^"]*\.mkv[^"]*)"/g,

        // Padrões para streaming
        /"(https?:\/\/[^"]*stream[^"]*\.(mp4|webm|m3u8)[^"]*)"/g,
        /"(https?:\/\/[^"]*video[^"]*\.(mp4|webm)[^"]*)"/g,

        // Padrões específicos para sites populares
        /"progressive":\s*\[([^\]]+)\]/g, // Vimeo
        /"formats":\s*\[([^\]]+)\]/g, // YouTube/outros
      ];

      for (const pattern of videoPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let videoUrl = match[1];

          // Tratar casos especiais (arrays JSON)
          if (pattern.source.includes('progressive') || pattern.source.includes('formats')) {
            try {
              const dataArray = JSON.parse(`[${match[1]}]`);
              dataArray.forEach(item => {
                if (item.url && this.isValidVideoUrl(item.url)) {
                  const decodedUrl = this.decodeUrl(item.url);
                  foundUrls.add(decodedUrl);
                }
              });
              continue;
            } catch (e) {
              // Se falhar no parse, continuar com outros padrões
              continue;
            }
          }

          if (videoUrl && this.isValidVideoUrl(videoUrl)) {
            const decodedUrl = this.decodeUrl(videoUrl);
            foundUrls.add(decodedUrl);
          }
        }
      }

      // Verificar acessibilidade das URLs encontradas
      const accessibleUrls = [];
      for (const videoUrl of foundUrls) {
        try {
          if (await this.isUrlAccessible(videoUrl)) {
            accessibleUrls.push(videoUrl);
          }
        } catch (e) {
          console.log('URL não acessível:', videoUrl.substring(0, 50));
        }
      }

      if (accessibleUrls.length > 0) {
        console.log(`Encontradas ${accessibleUrls.length} URLs de vídeo acessíveis`);
        // Retornar a primeira URL acessível ou todas se houver múltiplas
        return accessibleUrls.length === 1 ? accessibleUrls[0] : accessibleUrls;
      }

      return null;
    } catch (error) {
      console.error('Erro ao extrair URL do vídeo:', error);
      return null;
    }
  }

  // Verificar se a URL é um vídeo válido (mais abrangente)
  isValidVideoUrl(url) {
    if (!url || typeof url !== 'string') return false;

    const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.ogv', '.m3u8'];
    const lowerUrl = url.toLowerCase();

    return videoExtensions.some(ext => lowerUrl.includes(ext)) ||
           lowerUrl.includes('video') ||
           lowerUrl.includes('stream') ||
           lowerUrl.includes('media') ||
           (url.startsWith('http') && (lowerUrl.includes('mp4') || lowerUrl.includes('webm')));
  }

  // Verificar se a URL é acessível
  async isUrlAccessible(url) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 segundos timeout

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      clearTimeout(timeoutId);

      // Verificar se é realmente um vídeo
      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');

      console.log(`URL check: ${url.substring(0, 50)}... - Status: ${response.status}, Type: ${contentType}, Size: ${contentLength}`);

      return response.ok && (
        (contentType && contentType.includes('video')) ||
        this.isValidVideoUrl(url) ||
        (contentLength && parseInt(contentLength) > 100000) // Pelo menos 100KB
      );
    } catch (error) {
      console.log('Erro ao verificar URL:', error.message);
      return false;
    }
  }

  // Decodificar URL escapada
  decodeUrl(url) {
    try {
      return decodeURIComponent(url.replace(/\\u/g, '%u').replace(/\\/g, ''));
    } catch {
      return url;
    }
  }

  // Obter informações do arquivo antes do download
  async getVideoInfo(url) {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      const contentType = response.headers.get('content-type');
      
      return {
        size: contentLength ? parseInt(contentLength) : null,
        type: contentType,
        isVideo: contentType && contentType.includes('video')
      };
    } catch {
      return { size: null, type: null, isVideo: true };
    }
  }

  // Gerar nome único para o arquivo
  generateFileName(url, info = {}) {
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 1000);
    
    // Tentar extrair extensão do content-type
    let extension = '.mp4';
    if (info.type) {
      if (info.type.includes('webm')) extension = '.webm';
      else if (info.type.includes('avi')) extension = '.avi';
      else if (info.type.includes('mov')) extension = '.mov';
    }

    return `video_${timestamp}_${random}${extension}`;
  }

  // Função principal de download com múltiplas estratégias
  async downloadVideo(url, progressCallback) {
    try {
      console.log('Iniciando download de:', url);

      // Analisar URL
      const urlInfo = await this.analyzeUrl(url);
      let attempts = [];

      // Estratégia 1: Se a URL já é um vídeo direto, usar diretamente
      if (this.isValidVideoUrl(url)) {
        console.log('URL detectada como vídeo direto');
        attempts.push({ url: url, strategy: 'direct_video' });
      } else {
        // Estratégia 2: Tentar extrair URL de vídeo do HTML
        console.log('Tentando extrair URL de vídeo da página');
        try {
          const extractedUrls = await this.extractDirectVideoUrl(url);
          if (extractedUrls) {
            if (Array.isArray(extractedUrls)) {
              extractedUrls.forEach((extractedUrl, index) => {
                attempts.push({ url: extractedUrl, strategy: `extracted_${index}` });
              });
            } else {
              attempts.push({ url: extractedUrls, strategy: 'extracted' });
            }
          }
        } catch (e) {
          console.log('Falha na extração:', e.message);
        }

        // Estratégia 3: Fallback para URL original
        if (attempts.length === 0) {
          console.log('Usando URL original como fallback');
          attempts.push({ url: url, strategy: 'fallback_original' });
        }
      }

      let lastError = null;

      // Tentar cada estratégia
      for (const attempt of attempts) {
        try {
          console.log(`Tentando estratégia: ${attempt.strategy} com URL: ${attempt.url.substring(0, 100)}...`);

          // Verificar se a URL é acessível
          const isAccessible = await this.isUrlAccessible(attempt.url);
          if (!isAccessible) {
            throw new Error('URL não acessível');
          }

          // Obter informações do vídeo
          const videoInfo = await this.getVideoInfo(attempt.url);
          console.log('Informações do vídeo:', videoInfo);

          // Verificar se realmente é um vídeo
          if (!videoInfo.isVideo && !this.isValidVideoUrl(attempt.url)) {
            throw new Error('Não é um arquivo de vídeo válido');
          }

          // Gerar nome do arquivo
          const fileName = this.generateFileName(attempt.url, videoInfo);
          const fileUri = FileSystem.documentDirectory + fileName;

          console.log('Iniciando download para:', fileName);

          // Criar download resumível com headers apropriados
          const downloadResumable = FileSystem.createDownloadResumable(
            attempt.url,
            fileUri,
            {
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': urlInfo.originalUrl || url,
                'Accept': 'video/webm,video/ogg,video/*;q=0.9,application/ogg;q=0.7,audio/*;q=0.6,*/*;q=0.5',
                'Accept-Encoding': 'identity',
                'Range': 'bytes=0-'
              }
            },
            progressCallback
          );

          // Executar download
          const result = await downloadResumable.downloadAsync();

          if (!result || !result.uri) {
            throw new Error('Falha no download do arquivo');
          }

          // Verificar se o arquivo foi baixado corretamente
          const fileInfo = await FileSystem.getInfoAsync(result.uri);
          console.log('Arquivo baixado:', fileInfo);

          if (!fileInfo.exists) {
            throw new Error('Arquivo não foi criado');
          }

          if (fileInfo.size < 10000) { // Menos de 10KB provavelmente é um erro
            throw new Error(`Arquivo muito pequeno (${fileInfo.size} bytes) - provavelmente não é o vídeo real`);
          }

          console.log(`Sucesso com estratégia: ${attempt.strategy}, tamanho: ${fileInfo.size} bytes`);
          return {
            uri: result.uri,
            fileName,
            size: fileInfo.size,
            originalUrl: url,
            downloadUrl: attempt.url,
            strategy: attempt.strategy
          };

        } catch (error) {
          console.log(`Falha na estratégia ${attempt.strategy}:`, error.message);
          lastError = error;

          // Tentar limpar arquivo parcial se existir
          try {
            const fileName = this.generateFileName(attempt.url, {});
            const fileUri = FileSystem.documentDirectory + fileName;
            await FileSystem.deleteAsync(fileUri, { idempotent: true });
          } catch (cleanupError) {
            // Ignorar erros de limpeza
          }

          continue;
        }
      }

      // Se chegou aqui, todas as estratégias falharam
      throw lastError || new Error('Não foi possível baixar o vídeo de nenhuma fonte encontrada');

    } catch (error) {
      console.error('Erro no download:', error);
      throw error;
    }
  }

  // Salvar vídeo na galeria
  async saveToGallery(fileUri, fileName) {
    try {
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      
      // Criar álbum se não existir
      const albums = await MediaLibrary.getAlbumsAsync();
      let album = albums.find(a => a.title === 'Video Downloader');
      
      if (!album) {
        album = await MediaLibrary.createAlbumAsync('Video Downloader', asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }

      return asset;
    } catch (error) {
      console.error('Erro ao salvar na galeria:', error);
      throw new Error('Não foi possível salvar o vídeo na galeria');
    }
  }

  // Limpar arquivos temporários
  async cleanupTempFiles() {
    try {
      const dirInfo = await FileSystem.readDirectoryAsync(FileSystem.documentDirectory);
      const videoFiles = dirInfo.filter(file => file.startsWith('video_'));
      
      for (const file of videoFiles) {
        const filePath = FileSystem.documentDirectory + file;
        const fileInfo = await FileSystem.getInfoAsync(filePath);
        
        // Remover arquivos mais antigos que 1 hora
        if (fileInfo.exists && Date.now() - fileInfo.modificationTime > 3600000) {
          await FileSystem.deleteAsync(filePath);
        }
      }
    } catch (error) {
      console.error('Erro ao limpar arquivos temporários:', error);
    }
  }
}

export default new VideoDownloadService();
