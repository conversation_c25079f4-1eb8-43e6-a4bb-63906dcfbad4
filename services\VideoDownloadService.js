import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';

class VideoDownloadService {
  constructor() {
    // Removido: lista de sites permitidos - agora aceita qualquer URL
  }

  // Detectar tipo de site e extrair informações do vídeo
  async analyzeUrl(url) {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase().replace('www.', '');

      // Aceitar qualquer URL válida - sem restrições de site
      return {
        hostname,
        isSupported: true, // Sempre true - aceita qualquer site
        originalUrl: url,
        cleanUrl: this.cleanUrl(url)
      };
    } catch (error) {
      throw new Error('URL inválida');
    }
  }

  // Limpar URL removendo parâmetros desnecessários
  cleanUrl(url) {
    try {
      const urlObj = new URL(url);
      
      // Para YouTube, manter apenas o parâmetro 'v'
      if (urlObj.hostname.includes('youtube.com')) {
        const videoId = urlObj.searchParams.get('v');
        if (videoId) {
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }
      
      // Para youtu.be, converter para formato padrão
      if (urlObj.hostname.includes('youtu.be')) {
        const videoId = urlObj.pathname.slice(1);
        return `https://www.youtube.com/watch?v=${videoId}`;
      }

      return url;
    } catch {
      return url;
    }
  }

  // Tentar extrair URL direto do vídeo de qualquer site
  async extractDirectVideoUrl(url) {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      const html = await response.text();

      // Padrões mais abrangentes para encontrar vídeos em qualquer site
      const videoPatterns = [
        // URLs diretas de vídeo
        /"(https?:\/\/[^"]*\.mp4[^"]*)"/g,
        /"(https?:\/\/[^"]*\.webm[^"]*)"/g,
        /"(https?:\/\/[^"]*\.avi[^"]*)"/g,
        /"(https?:\/\/[^"]*\.mov[^"]*)"/g,
        /"(https?:\/\/[^"]*\.mkv[^"]*)"/g,

        // Padrões comuns de sites de vídeo
        /"url":"([^"]*video[^"]*)"/g,
        /"videoUrl":"([^"]*)"/g,
        /"src":"([^"]*\.(mp4|webm|avi|mov)[^"]*)"/g,
        /src="([^"]*\.(mp4|webm|avi|mov)[^"]*)"/g,
        /"contentUrl":"([^"]*)"/g,
        /"video_url":"([^"]*)"/g,
        /"file":"([^"]*\.(mp4|webm|avi|mov)[^"]*)"/g,

        // Padrões para streaming
        /"(https?:\/\/[^"]*stream[^"]*\.(mp4|webm|m3u8)[^"]*)"/g,
        /"(https?:\/\/[^"]*video[^"]*\.(mp4|webm)[^"]*)"/g,
      ];

      for (const pattern of videoPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const videoUrl = match[1];
          if (videoUrl && this.isValidVideoUrl(videoUrl)) {
            const decodedUrl = this.decodeUrl(videoUrl);
            // Verificar se a URL é acessível
            if (await this.isUrlAccessible(decodedUrl)) {
              return decodedUrl;
            }
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Erro ao extrair URL do vídeo:', error);
      return null;
    }
  }

  // Verificar se a URL é um vídeo válido (mais abrangente)
  isValidVideoUrl(url) {
    if (!url || typeof url !== 'string') return false;

    const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.ogv', '.m3u8'];
    const lowerUrl = url.toLowerCase();

    return videoExtensions.some(ext => lowerUrl.includes(ext)) ||
           lowerUrl.includes('video') ||
           lowerUrl.includes('stream') ||
           lowerUrl.includes('media') ||
           (url.startsWith('http') && (lowerUrl.includes('mp4') || lowerUrl.includes('webm')));
  }

  // Verificar se a URL é acessível
  async isUrlAccessible(url) {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        timeout: 5000
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  // Decodificar URL escapada
  decodeUrl(url) {
    try {
      return decodeURIComponent(url.replace(/\\u/g, '%u').replace(/\\/g, ''));
    } catch {
      return url;
    }
  }

  // Obter informações do arquivo antes do download
  async getVideoInfo(url) {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      const contentType = response.headers.get('content-type');
      
      return {
        size: contentLength ? parseInt(contentLength) : null,
        type: contentType,
        isVideo: contentType && contentType.includes('video')
      };
    } catch {
      return { size: null, type: null, isVideo: true };
    }
  }

  // Gerar nome único para o arquivo
  generateFileName(url, info = {}) {
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 1000);
    
    // Tentar extrair extensão do content-type
    let extension = '.mp4';
    if (info.type) {
      if (info.type.includes('webm')) extension = '.webm';
      else if (info.type.includes('avi')) extension = '.avi';
      else if (info.type.includes('mov')) extension = '.mov';
    }

    return `video_${timestamp}_${random}${extension}`;
  }

  // Função principal de download com múltiplas estratégias
  async downloadVideo(url, progressCallback) {
    try {
      // Analisar URL
      const urlInfo = await this.analyzeUrl(url);
      let downloadUrl = url;
      let attempts = [];

      // Estratégia 1: Tentar URL original se parecer ser um vídeo direto
      if (this.isValidVideoUrl(url)) {
        attempts.push({ url: url, strategy: 'direct_original' });
      }

      // Estratégia 2: Tentar extrair URL de vídeo do HTML
      try {
        const extractedUrl = await this.extractDirectVideoUrl(url);
        if (extractedUrl && extractedUrl !== url) {
          attempts.push({ url: extractedUrl, strategy: 'extracted' });
        }
      } catch (e) {
        console.log('Falha na extração:', e.message);
      }

      // Estratégia 3: Se nenhuma das anteriores, tentar a URL original mesmo assim
      if (attempts.length === 0) {
        attempts.push({ url: url, strategy: 'fallback_original' });
      }

      let lastError = null;

      // Tentar cada estratégia
      for (const attempt of attempts) {
        try {
          console.log(`Tentando estratégia: ${attempt.strategy} com URL: ${attempt.url}`);

          // Obter informações do vídeo
          const videoInfo = await this.getVideoInfo(attempt.url);

          // Gerar nome do arquivo
          const fileName = this.generateFileName(attempt.url, videoInfo);
          const fileUri = FileSystem.documentDirectory + fileName;

          // Criar download resumível
          const downloadResumable = FileSystem.createDownloadResumable(
            attempt.url,
            fileUri,
            {
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': urlInfo.originalUrl,
                'Accept': 'video/webm,video/ogg,video/*;q=0.9,application/ogg;q=0.7,audio/*;q=0.6,*/*;q=0.5'
              }
            },
            progressCallback
          );

          // Executar download
          const result = await downloadResumable.downloadAsync();

          if (!result || !result.uri) {
            throw new Error('Falha no download do arquivo');
          }

          // Verificar se o arquivo foi baixado corretamente
          const fileInfo = await FileSystem.getInfoAsync(result.uri);
          if (!fileInfo.exists || fileInfo.size < 1000) {
            throw new Error('Arquivo baixado está corrompido ou muito pequeno');
          }

          console.log(`Sucesso com estratégia: ${attempt.strategy}`);
          return {
            uri: result.uri,
            fileName,
            size: fileInfo.size,
            originalUrl: url,
            strategy: attempt.strategy
          };

        } catch (error) {
          console.log(`Falha na estratégia ${attempt.strategy}:`, error.message);
          lastError = error;
          continue;
        }
      }

      // Se chegou aqui, todas as estratégias falharam
      throw lastError || new Error('Não foi possível baixar o vídeo de nenhuma fonte encontrada');

    } catch (error) {
      console.error('Erro no download:', error);
      throw error;
    }
  }

  // Salvar vídeo na galeria
  async saveToGallery(fileUri, fileName) {
    try {
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      
      // Criar álbum se não existir
      const albums = await MediaLibrary.getAlbumsAsync();
      let album = albums.find(a => a.title === 'Video Downloader');
      
      if (!album) {
        album = await MediaLibrary.createAlbumAsync('Video Downloader', asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }

      return asset;
    } catch (error) {
      console.error('Erro ao salvar na galeria:', error);
      throw new Error('Não foi possível salvar o vídeo na galeria');
    }
  }

  // Limpar arquivos temporários
  async cleanupTempFiles() {
    try {
      const dirInfo = await FileSystem.readDirectoryAsync(FileSystem.documentDirectory);
      const videoFiles = dirInfo.filter(file => file.startsWith('video_'));
      
      for (const file of videoFiles) {
        const filePath = FileSystem.documentDirectory + file;
        const fileInfo = await FileSystem.getInfoAsync(filePath);
        
        // Remover arquivos mais antigos que 1 hora
        if (fileInfo.exists && Date.now() - fileInfo.modificationTime > 3600000) {
          await FileSystem.deleteAsync(filePath);
        }
      }
    } catch (error) {
      console.error('Erro ao limpar arquivos temporários:', error);
    }
  }
}

export default new VideoDownloadService();
