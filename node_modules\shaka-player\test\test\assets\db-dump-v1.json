{"version": 1, "stores": {"manifest": {"parameters": {"keyPath": "key", "autoIncrement": false}, "data": [{"value": {"key": 0, "originalManifestUri": "//storage.googleapis.com/shaka-demo-assets/heliocentrism/heliocentrism.mpd", "duration": 4.904831, "size": 456654, "expiration": null, "periods": [{"startTime": 0, "streams": [{"id": 5, "primary": false, "presentationTimeOffset": 0, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "language": "und", "label": null, "width": 640, "height": 480, "initSegmentUri": "offline:0/5/1", "encrypted": false, "keyId": null, "segments": [{"startTime": 0, "endTime": 2.06874, "uri": "offline:0/5/0"}], "variantIds": [13]}]}, {"startTime": 2.06874, "streams": [{"id": 24, "primary": false, "presentationTimeOffset": 0, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "language": "und", "label": null, "width": 640, "height": 480, "initSegmentUri": "offline:0/24/3", "encrypted": false, "keyId": null, "segments": [{"startTime": 0, "endTime": 2.13539, "uri": "offline:0/24/2"}], "variantIds": [32]}]}, {"startTime": 4.20413, "streams": [{"id": 35, "primary": false, "presentationTimeOffset": 0, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "language": "und", "label": null, "width": 320, "height": 240, "initSegmentUri": "offline:0/35/5", "encrypted": false, "keyId": null, "segments": [{"startTime": 0, "endTime": 0.700701, "uri": "offline:0/35/4"}], "variantIds": [39]}]}], "sessionIds": [], "drmInfo": null, "appMetadata": {"name": "[OFFLINE] Heliocentrism (multicodec, multiperiod)"}}}]}, "segment": {"parameters": {"keyPath": "key", "autoIncrement": false}, "data": [{"value": {"key": 0, "data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}, "manifestKey": 0, "streamNumber": 5, "segmentNumber": 0}}, {"value": {"key": 1, "data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}, "manifestKey": 0, "streamNumber": 5, "segmentNumber": -1}}, {"value": {"key": 2, "data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}, "manifestKey": 0, "streamNumber": 24, "segmentNumber": 2}}, {"value": {"key": 3, "data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}, "manifestKey": 0, "streamNumber": 24, "segmentNumber": -1}}, {"value": {"key": 4, "data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}, "manifestKey": 0, "streamNumber": 35, "segmentNumber": 4}}, {"value": {"key": 5, "data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}, "manifestKey": 0, "streamNumber": 35, "segmentNumber": -1}}]}}}