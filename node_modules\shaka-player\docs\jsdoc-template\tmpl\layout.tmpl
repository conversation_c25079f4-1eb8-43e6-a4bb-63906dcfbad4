<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: <?js= title ?></title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <script src="scripts/show-widget.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

<?js if (title.indexOf('Tutorial:') == -1) { ?>
    <h1 class="page-title"><?js= title ?></h1>
<?js } ?>

    <?js= content ?>
</div>

<nav>
    <div id="showWidget">Show:
        <select id="show" onchange="onShowChange()">
            <option value="exported">exported</option>
            <option value="public">public</option>
            <option value="private">everything</option>
        </select>
    </div>

    <?js= this.nav ?>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc <?js= env.version.number ?></a><?js if(env.conf.templates && env.conf.templates.default && env.conf.templates.default.includeDate !== false) { ?> on <?js= (new Date()) ?><?js } ?>
</footer>

<script> initShowWidget(); </script>
<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>
