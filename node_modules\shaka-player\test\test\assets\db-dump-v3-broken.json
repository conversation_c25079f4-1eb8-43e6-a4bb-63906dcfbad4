{"version": 4, "stores": {"session-ids": {"parameters": {"keyPath": null, "autoIncrement": true}, "data": []}, "manifest-v3": {"parameters": {"keyPath": null, "autoIncrement": true}, "data": [{"value": {"originalManifestUri": "https://storage.googleapis.com/shaka-demo-assets/heliocentrism/heliocentrism.mpd", "duration": 4.904831, "size": 1346466, "expiration": null, "periods": [{"startTime": 0, "streams": [{"id": 5, "originalId": "02", "primary": false, "presentationTimeOffset": 0, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "pixelAspectRatio": "4:3", "language": "und", "label": null, "width": 640, "height": 480, "initSegmentKey": 3, "encrypted": false, "keyId": null, "segments": [{"startTime": 0, "endTime": 2.06874, "dataKey": 6}], "variantIds": [13]}, {"id": 24, "originalId": "17", "primary": false, "presentationTimeOffset": -2.06874, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "pixelAspectRatio": "4:3", "language": "und", "label": null, "width": 640, "height": 480, "initSegmentKey": 1, "encrypted": false, "keyId": null, "segments": [{"startTime": 2.06874, "endTime": 4.20413, "dataKey": 5}], "variantIds": []}, {"id": 33, "originalId": "20", "primary": false, "presentationTimeOffset": -4.20413, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "pixelAspectRatio": "4:3", "language": "und", "label": null, "width": 320, "height": 240, "initSegmentKey": 2, "encrypted": false, "keyId": null, "segments": [{"startTime": 4.20413, "endTime": 4.904831, "dataKey": 4}], "variantIds": []}]}, {"startTime": 2.06874, "streams": [{"id": 5, "originalId": "02", "primary": false, "presentationTimeOffset": 2.06874, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "pixelAspectRatio": "4:3", "language": "und", "label": null, "width": 640, "height": 480, "initSegmentKey": 8, "encrypted": false, "keyId": null, "segments": [{"startTime": -2.06874, "endTime": 0, "dataKey": 12}], "variantIds": []}, {"id": 24, "originalId": "17", "primary": false, "presentationTimeOffset": 0, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "pixelAspectRatio": "4:3", "language": "und", "label": null, "width": 640, "height": 480, "initSegmentKey": 9, "encrypted": false, "keyId": null, "segments": [{"startTime": 0, "endTime": 2.13539, "dataKey": 11}], "variantIds": [32]}, {"id": 33, "originalId": "20", "primary": false, "presentationTimeOffset": -2.13539, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "pixelAspectRatio": "4:3", "language": "und", "label": null, "width": 320, "height": 240, "initSegmentKey": 7, "encrypted": false, "keyId": null, "segments": [{"startTime": 2.13539, "endTime": 2.8360909999999997, "dataKey": 10}], "variantIds": []}]}, {"startTime": 4.20413, "streams": [{"id": 5, "originalId": "02", "primary": false, "presentationTimeOffset": 4.20413, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "pixelAspectRatio": "4:3", "language": "und", "label": null, "width": 640, "height": 480, "initSegmentKey": 15, "encrypted": false, "keyId": null, "segments": [{"startTime": -4.20413, "endTime": -2.13539, "dataKey": 18}], "variantIds": []}, {"id": 24, "originalId": "17", "primary": false, "presentationTimeOffset": 2.13539, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "pixelAspectRatio": "4:3", "language": "und", "label": null, "width": 640, "height": 480, "initSegmentKey": 14, "encrypted": false, "keyId": null, "segments": [{"startTime": -2.13539, "endTime": 0, "dataKey": 17}], "variantIds": []}, {"id": 33, "originalId": "20", "primary": false, "presentationTimeOffset": 0, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "pixelAspectRatio": "4:3", "language": "und", "label": null, "width": 320, "height": 240, "initSegmentKey": 13, "encrypted": false, "keyId": null, "segments": [{"startTime": 0, "endTime": 0.7007009999999996, "dataKey": 16}], "variantIds": [37]}]}], "sessionIds": [], "drmInfo": null, "appMetadata": {"identifier": "Heliocentrism (multicodec, multiperiod)", "downloaded": "2020-02-11T00:05:38.526Z"}}, "key": 1}]}, "segment-v3": {"parameters": {"keyPath": null, "autoIncrement": true}, "data": [{"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 1}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 2}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 3}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 4}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 5}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 6}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 7}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 8}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 9}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 10}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 11}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 12}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 13}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 14}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 15}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 16}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 17}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 18}]}}}