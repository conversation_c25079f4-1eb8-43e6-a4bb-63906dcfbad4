{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "CompatNativeSafeAreaProvider", "children", "style", "onInsetsChange", "window", "useWindowDimensions", "useEffect", "insets", "top", "bottom", "left", "right", "frame", "x", "y", "width", "height", "nativeEvent", "createElement", "View"], "sourceRoot": "../../src", "sources": ["CompatNativeSafeAreaProvider.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAyD,SAAAE,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGlD,SAASW,4BAA4BA,CAAC;EAC3CC,QAAQ;EACRC,KAAK;EACLC;AAC2B,CAAC,EAAE;EAC9B,MAAMC,MAAM,GAAG,IAAAC,gCAAmB,EAAC,CAAC;EACpC7B,KAAK,CAAC8B,SAAS,CAAC,MAAM;IACpB,MAAMC,MAAM,GAAG;MACbC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACT,CAAC;IACD,MAAMC,KAAK,GAAG;MACZC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAEX,MAAM,CAACW,KAAK;MACnBC,MAAM,EAAEZ,MAAM,CAACY;IACjB,CAAC;IACD;IACAb,cAAc,CAAC;MAAEc,WAAW,EAAE;QAAEV,MAAM;QAAEK;MAAM;IAAE,CAAC,CAAC;EACpD,CAAC,EAAE,CAACT,cAAc,EAAEC,MAAM,CAACY,MAAM,EAAEZ,MAAM,CAACW,KAAK,CAAC,CAAC;EACjD,oBAAOvC,KAAA,CAAA0C,aAAA,CAACvC,YAAA,CAAAwC,IAAI;IAACjB,KAAK,EAAEA;EAAM,GAAED,QAAe,CAAC;AAC9C", "ignoreList": []}