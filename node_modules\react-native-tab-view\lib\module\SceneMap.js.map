{"version": 3, "names": ["React", "jsx", "_jsx", "SceneComponent", "memo", "component", "rest", "createElement", "displayName", "SceneMap", "scenes", "route", "jumpTo", "position", "key"], "sourceRoot": "../../src", "sources": ["SceneMap.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAQ/B,MAAMC,cAAc,gBAAGH,KAAK,CAACI,IAAI,CAC/B,CAAiE;EAC/DC,SAAS;EACT,GAAGC;AACF,CAAC,KAAK;EACP,oBAAON,KAAK,CAACO,aAAa,CAACF,SAAS,EAAEC,IAAI,CAAC;AAC7C,CACF,CAAC;AAEDH,cAAc,CAACK,WAAW,GAAG,gBAAgB;AAE7C,OAAO,SAASC,QAAQA,CAAIC,MAAiD,EAAE;EAC7E,OAAO,CAAC;IAAEC,KAAK;IAAEC,MAAM;IAAEC;EAAqB,CAAC,kBAC7CX,IAAA,CAACC,cAAc;IAEbE,SAAS,EAAEK,MAAM,CAACC,KAAK,CAACG,GAAG,CAAE;IAC7BH,KAAK,EAAEA,KAAM;IACbC,MAAM,EAAEA,MAAO;IACfC,QAAQ,EAAEA;EAAS,GAJdF,KAAK,CAACG,GAKZ,CACF;AACH", "ignoreList": []}