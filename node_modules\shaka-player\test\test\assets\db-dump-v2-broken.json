{"version": 2, "stores": {"manifest": {"parameters": {"keyPath": "key", "autoIncrement": false}, "data": []}, "segment": {"parameters": {"keyPath": "key", "autoIncrement": false}, "data": []}, "manifest-v2": {"parameters": {"keyPath": null, "autoIncrement": false}, "data": [{"value": {"originalManifestUri": "//storage.googleapis.com/shaka-demo-assets/heliocentrism/heliocentrism.mpd", "duration": 4.904831, "size": 456654, "expiration": null, "periods": [{"startTime": 0, "streams": [{"id": 5, "primary": false, "presentationTimeOffset": 0, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "language": "und", "label": null, "width": 640, "height": 480, "initSegmentKey": 1, "encrypted": false, "keyId": null, "segments": [{"startTime": 0, "endTime": 2.06874, "dataKey": 0}], "variantIds": [13]}]}, {"startTime": 2.06874, "streams": [{"id": 24, "primary": false, "presentationTimeOffset": 0, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "language": "und", "label": null, "width": 640, "height": 480, "initSegmentKey": 3, "encrypted": false, "keyId": null, "segments": [{"startTime": 0, "endTime": 2.13539, "dataKey": 2}], "variantIds": [32]}]}, {"startTime": 4.20413, "streams": [{"id": 35, "primary": false, "presentationTimeOffset": 0, "contentType": "video", "mimeType": "video/webm", "codecs": "vp9", "frameRate": 29.97, "language": "und", "label": null, "width": 320, "height": 240, "initSegmentKey": 5, "encrypted": false, "keyId": null, "segments": [{"startTime": 0, "endTime": 0.700701, "dataKey": 4}], "variantIds": [39]}]}], "sessionIds": [], "drmInfo": null, "appMetadata": {"name": "[OFFLINE] Heliocentrism (multicodec, multiperiod)"}}, "key": 0}]}, "segment-v2": {"parameters": {"keyPath": null, "autoIncrement": false}, "data": [{"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 0}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 1}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 2}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 3}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 4}, {"value": {"data": {"__type__": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__value__": ""}}, "key": 5}]}}}