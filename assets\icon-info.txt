ÍCONES NECESSÁRIOS PARA O APLICATIVO:

1. icon.png (1024x1024) - Ícone principal do aplicativo
2. adaptive-icon.png (1024x1024) - Ícone adaptativo para Android
3. splash.png (1284x2778) - Tela de splash
4. favicon.png (48x48) - Favicon para web

DESCRIÇÃO DO DESIGN:
- Cor principal: #4CAF50 (verde)
- Cor de fundo: #1a1a2e (azul escuro)
- Ícone: Seta para baixo estilizada com símbolo de vídeo
- Estilo: Moderno, minimalista, flat design

COMO CRIAR OS ÍCONES:
1. Use um editor de imagens como Figma, Photoshop ou Canva
2. Crie um círculo com gradiente do verde (#4CAF50) para verde claro
3. Adicione uma seta para baixo branca no centro
4. Adicione um pequeno símbolo de play ou câmera
5. Exporte nas dimensões necessárias

ALTERNATIVA RÁPIDA:
Use o gerador de ícones online do Expo:
https://docs.expo.dev/guides/app-icons/

Ou use o comando:
npx @expo/image-utils generate-icons --icon-path ./path-to-your-icon.png
