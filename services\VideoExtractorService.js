// Serviço especializado para extração de vídeos de diferentes plataformas
class VideoExtractorService {
  
  // Extrair vídeos do YouTube
  static async extractYouTubeVideo(url) {
    try {
      console.log('Extraindo vídeo do YouTube:', url);
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const html = await response.text();
      
      // Procurar por ytInitialPlayerResponse
      const playerMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
      if (playerMatch) {
        try {
          const playerData = JSON.parse(playerMatch[1]);
          const streamingData = playerData.streamingData;
          
          if (streamingData) {
            const videoUrls = [];
            
            // Formatos progressivos (vídeo + áudio)
            if (streamingData.formats) {
              streamingData.formats.forEach(format => {
                if (format.url && format.qualityLabel) {
                  videoUrls.push({
                    url: format.url,
                    quality: format.qualityLabel,
                    hasAudio: true,
                    filesize: format.contentLength
                  });
                }
              });
            }
            
            // Se não houver formatos progressivos, usar adaptativos
            if (videoUrls.length === 0 && streamingData.adaptiveFormats) {
              const videoFormats = streamingData.adaptiveFormats.filter(f => 
                f.mimeType?.includes('video') && f.url
              );
              
              videoFormats.forEach(format => {
                if (format.qualityLabel) {
                  videoUrls.push({
                    url: format.url,
                    quality: format.qualityLabel,
                    hasAudio: false,
                    filesize: format.contentLength
                  });
                }
              });
            }
            
            return videoUrls;
          }
        } catch (e) {
          console.error('Erro ao analisar dados do YouTube:', e);
        }
      }
      
      return [];
    } catch (error) {
      console.error('Erro na extração do YouTube:', error);
      return [];
    }
  }
  
  // Extrair vídeos do Vimeo
  static async extractVimeoVideo(url) {
    try {
      console.log('Extraindo vídeo do Vimeo:', url);
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const html = await response.text();
      
      // Procurar por config_url
      const configMatch = html.match(/"config_url":"([^"]*)"/);
      if (configMatch) {
        try {
          const configUrl = configMatch[1].replace(/\\u002F/g, '/').replace(/\\/g, '');
          const configResponse = await fetch(configUrl);
          const config = await configResponse.json();
          
          const videoUrls = [];
          
          if (config.request?.files?.progressive) {
            config.request.files.progressive.forEach(file => {
              videoUrls.push({
                url: file.url,
                quality: `${file.height}p`,
                hasAudio: true,
                filesize: file.size
              });
            });
          }
          
          return videoUrls;
        } catch (e) {
          console.error('Erro ao obter config do Vimeo:', e);
        }
      }
      
      return [];
    } catch (error) {
      console.error('Erro na extração do Vimeo:', error);
      return [];
    }
  }
  
  // Extrair vídeos de sites genéricos
  static async extractGenericVideo(url) {
    try {
      console.log('Extraindo vídeo genérico:', url);
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const html = await response.text();
      const videoUrls = [];
      
      // Padrões para encontrar vídeos
      const patterns = [
        // JSON com URLs de vídeo
        /"url":"([^"]*\.mp4[^"]*)"/g,
        /"src":"([^"]*\.mp4[^"]*)"/g,
        /"file":"([^"]*\.mp4[^"]*)"/g,
        
        // Tags HTML
        /<video[^>]*src="([^"]*\.mp4[^"]*)"/g,
        /<source[^>]*src="([^"]*\.mp4[^"]*)"/g,
        
        // URLs completas
        /"(https?:\/\/[^"]*\.mp4[^"]*)"/g,
        /"(https?:\/\/[^"]*\.webm[^"]*)"/g
      ];
      
      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const videoUrl = match[1];
          if (videoUrl && this.isValidVideoUrl(videoUrl)) {
            videoUrls.push({
              url: this.decodeVideoUrl(videoUrl),
              quality: 'Original',
              hasAudio: true,
              filesize: null
            });
          }
        }
      }
      
      // Remover duplicatas
      const uniqueUrls = videoUrls.filter((video, index, self) => 
        index === self.findIndex(v => v.url === video.url)
      );
      
      return uniqueUrls;
    } catch (error) {
      console.error('Erro na extração genérica:', error);
      return [];
    }
  }
  
  // Verificar se é uma URL de vídeo válida
  static isValidVideoUrl(url) {
    if (!url || typeof url !== 'string') return false;
    
    const videoExtensions = ['.mp4', '.webm', '.avi', '.mov', '.mkv', '.m4v'];
    const lowerUrl = url.toLowerCase();
    
    return videoExtensions.some(ext => lowerUrl.includes(ext)) ||
           lowerUrl.includes('video') ||
           lowerUrl.includes('stream');
  }
  
  // Decodificar URL de vídeo
  static decodeVideoUrl(url) {
    try {
      return decodeURIComponent(url.replace(/\\u0026/g, '&').replace(/\\/g, ''));
    } catch {
      return url;
    }
  }
  
  // Método principal para extrair vídeos de qualquer URL
  static async extractVideo(url) {
    try {
      const hostname = new URL(url).hostname.toLowerCase();
      
      // Escolher extrator baseado no site
      if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
        return await this.extractYouTubeVideo(url);
      } else if (hostname.includes('vimeo.com')) {
        return await this.extractVimeoVideo(url);
      } else {
        return await this.extractGenericVideo(url);
      }
    } catch (error) {
      console.error('Erro na extração de vídeo:', error);
      return [];
    }
  }
  
  // Verificar se uma URL de vídeo é acessível
  static async isVideoAccessible(videoUrl) {
    try {
      const response = await fetch(videoUrl, { 
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');
      
      return response.ok && (
        (contentType && contentType.includes('video')) ||
        this.isValidVideoUrl(videoUrl) ||
        (contentLength && parseInt(contentLength) > 50000)
      );
    } catch {
      return false;
    }
  }
  
  // Obter a melhor qualidade disponível
  static getBestQuality(videoUrls) {
    if (!videoUrls || videoUrls.length === 0) return null;
    
    // Priorizar vídeos com áudio
    const withAudio = videoUrls.filter(v => v.hasAudio);
    const toSort = withAudio.length > 0 ? withAudio : videoUrls;
    
    // Ordenar por qualidade (assumindo que qualidades maiores são melhores)
    return toSort.sort((a, b) => {
      const aHeight = parseInt(a.quality) || 0;
      const bHeight = parseInt(b.quality) || 0;
      return bHeight - aHeight;
    })[0];
  }
}

export default VideoExtractorService;
