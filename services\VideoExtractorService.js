// Serviço especializado para extração de vídeos de diferentes plataformas
class VideoExtractorService {
  
  // Extrair vídeos do YouTube
  static async extractYouTubeVideo(url) {
    try {
      console.log('Extraindo vídeo do YouTube:', url);
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const html = await response.text();
      
      // Procurar por ytInitialPlayerResponse
      const playerMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
      if (playerMatch) {
        try {
          const playerData = JSON.parse(playerMatch[1]);
          const streamingData = playerData.streamingData;
          
          if (streamingData) {
            const videoUrls = [];
            
            // Formatos progressivos (vídeo + áudio)
            if (streamingData.formats) {
              streamingData.formats.forEach(format => {
                if (format.url && format.qualityLabel) {
                  videoUrls.push({
                    url: format.url,
                    quality: format.qualityLabel,
                    hasAudio: true,
                    filesize: format.contentLength
                  });
                }
              });
            }
            
            // SEMPRE incluir formatos adaptativos para ter TODAS as qualidades
            if (streamingData.adaptiveFormats) {
              const videoFormats = streamingData.adaptiveFormats.filter(f =>
                f.mimeType?.includes('video') && f.url && f.qualityLabel
              );

              videoFormats.forEach(format => {
                // Só adicionar se não temos essa qualidade nos progressivos
                const existingQuality = videoUrls.find(v => v.quality === format.qualityLabel);
                if (!existingQuality) {
                  videoUrls.push({
                    url: format.url,
                    quality: format.qualityLabel,
                    hasAudio: false,
                    filesize: format.contentLength
                  });
                }
              });
            }
            
            return videoUrls;
          }
        } catch (e) {
          console.error('Erro ao analisar dados do YouTube:', e);
        }
      }
      
      return [];
    } catch (error) {
      console.error('Erro na extração do YouTube:', error);
      return [];
    }
  }
  
  // Extrair vídeos do Vimeo
  static async extractVimeoVideo(url) {
    try {
      console.log('Extraindo vídeo do Vimeo:', url);
      
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const html = await response.text();
      
      // Procurar por config_url
      const configMatch = html.match(/"config_url":"([^"]*)"/);
      if (configMatch) {
        try {
          const configUrl = configMatch[1].replace(/\\u002F/g, '/').replace(/\\/g, '');
          const configResponse = await fetch(configUrl);
          const config = await configResponse.json();
          
          const videoUrls = [];
          
          if (config.request?.files?.progressive) {
            config.request.files.progressive.forEach(file => {
              videoUrls.push({
                url: file.url,
                quality: `${file.height}p`,
                hasAudio: true,
                filesize: file.size
              });
            });
          }
          
          return videoUrls;
        } catch (e) {
          console.error('Erro ao obter config do Vimeo:', e);
        }
      }
      
      return [];
    } catch (error) {
      console.error('Erro na extração do Vimeo:', error);
      return [];
    }
  }
  
  // Extrair vídeos de sites genéricos com análise detalhada
  static async extractGenericVideo(url) {
    try {
      console.log('Extraindo vídeo genérico:', url);

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const html = await response.text();
      const videoUrls = [];

      // Padrões mais abrangentes com contexto
      const patterns = [
        // JSON com qualidade e URL
        /"quality":"([^"]*)"[^}]*"url":"([^"]*\.mp4[^"]*)"/g,
        /"url":"([^"]*\.mp4[^"]*)"[^}]*"quality":"([^"]*)"/g,
        /"height":(\d+)[^}]*"url":"([^"]*\.mp4[^"]*)"/g,
        /"url":"([^"]*\.mp4[^"]*)"[^}]*"height":(\d+)/g,

        // JSON simples
        /"url":"([^"]*\.mp4[^"]*)"/g,
        /"src":"([^"]*\.mp4[^"]*)"/g,
        /"file":"([^"]*\.mp4[^"]*)"/g,

        // Tags HTML com atributos
        /<video[^>]*src="([^"]*\.mp4[^"]*)"[^>]*data-quality="([^"]*)"/g,
        /<video[^>]*data-quality="([^"]*)"[^>]*src="([^"]*\.mp4[^"]*)"/g,
        /<video[^>]*src="([^"]*\.mp4[^"]*)"/g,
        /<source[^>]*src="([^"]*\.mp4[^"]*)"[^>]*data-quality="([^"]*)"/g,
        /<source[^>]*data-quality="([^"]*)"[^>]*src="([^"]*\.mp4[^"]*)"/g,
        /<source[^>]*src="([^"]*\.mp4[^"]*)"/g,

        // URLs completas com contexto
        /"(https?:\/\/[^"]*\.mp4[^"]*)"/g,
        /"(https?:\/\/[^"]*\.webm[^"]*)"/g,
        /"(https?:\/\/[^"]*\.avi[^"]*)"/g,
        /"(https?:\/\/[^"]*\.mov[^"]*)"/g
      ];

      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          let videoUrl, quality = 'Original';

          // Determinar URL e qualidade baseado no padrão
          if (pattern.source.includes('quality.*url')) {
            quality = match[1];
            videoUrl = match[2];
          } else if (pattern.source.includes('url.*quality')) {
            videoUrl = match[1];
            quality = match[2];
          } else if (pattern.source.includes('height.*url')) {
            quality = `${match[1]}p`;
            videoUrl = match[2];
          } else if (pattern.source.includes('url.*height')) {
            videoUrl = match[1];
            quality = `${match[2]}p`;
          } else {
            videoUrl = match[1];
          }

          if (videoUrl && this.isValidVideoUrl(videoUrl)) {
            const decodedUrl = this.decodeVideoUrl(videoUrl);

            // Tentar obter informações adicionais
            const videoInfo = await this.getVideoInfo(decodedUrl);

            videoUrls.push({
              url: decodedUrl,
              quality: quality,
              hasAudio: true, // Assumir que tem áudio por padrão
              filesize: videoInfo.filesize || null,
              context: this.getVideoContext(html, videoUrl) // Contexto da página
            });
          }
        }
      }

      // Remover duplicatas
      const uniqueUrls = videoUrls.filter((video, index, self) =>
        index === self.findIndex(v => v.url === video.url)
      );

      console.log(`Encontrados ${uniqueUrls.length} vídeos únicos`);
      return uniqueUrls;
    } catch (error) {
      console.error('Erro na extração genérica:', error);
      return [];
    }
  }

  // Obter informações básicas do vídeo (otimizado para velocidade)
  static async getVideoInfo(videoUrl) {
    // Pular verificação HTTP para acelerar - será verificado no download
    return {
      filesize: null,
      contentType: null,
      accessible: true // Assumir que é acessível para acelerar
    };
  }

  // Obter contexto do vídeo na página (para ajudar na detecção do principal)
  static getVideoContext(html, videoUrl) {
    try {
      // Encontrar a seção do HTML onde o vídeo aparece
      const videoIndex = html.indexOf(videoUrl);
      if (videoIndex === -1) return '';

      // Extrair contexto ao redor (500 caracteres antes e depois)
      const start = Math.max(0, videoIndex - 500);
      const end = Math.min(html.length, videoIndex + 500);
      const context = html.substring(start, end).toLowerCase();

      return context;
    } catch {
      return '';
    }
  }
  
  // Verificar se é uma URL de vídeo válida
  static isValidVideoUrl(url) {
    if (!url || typeof url !== 'string') return false;
    
    const videoExtensions = ['.mp4', '.webm', '.avi', '.mov', '.mkv', '.m4v'];
    const lowerUrl = url.toLowerCase();
    
    return videoExtensions.some(ext => lowerUrl.includes(ext)) ||
           lowerUrl.includes('video') ||
           lowerUrl.includes('stream');
  }
  
  // Decodificar URL de vídeo
  static decodeVideoUrl(url) {
    try {
      return decodeURIComponent(url.replace(/\\u0026/g, '&').replace(/\\/g, ''));
    } catch {
      return url;
    }
  }
  
  // Método principal para extrair vídeos de qualquer URL
  static async extractVideo(url) {
    try {
      const hostname = new URL(url).hostname.toLowerCase();

      // Escolher extrator baseado no site
      if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
        return await this.extractYouTubeVideo(url);
      } else if (hostname.includes('vimeo.com')) {
        return await this.extractVimeoVideo(url);
      } else {
        const allVideos = await this.extractGenericVideo(url);
        // Para sites genéricos, detectar o vídeo principal
        return this.detectMainVideo(allVideos, url);
      }
    } catch (error) {
      console.error('Erro na extração de vídeo:', error);
      return [];
    }
  }

  // Detectar o vídeo principal e agrupar suas qualidades
  static detectMainVideo(videoUrls, pageUrl) {
    if (!videoUrls || videoUrls.length === 0) return [];
    if (videoUrls.length === 1) return videoUrls;

    console.log(`Detectando vídeo principal entre ${videoUrls.length} vídeos encontrados`);

    // Calcular pontuação para cada vídeo
    const scoredVideos = videoUrls.map(video => ({
      ...video,
      score: this.calculateVideoScore(video, pageUrl)
    }));

    // Ordenar por pontuação (maior primeiro)
    scoredVideos.sort((a, b) => b.score - a.score);

    console.log('Pontuações dos vídeos:', scoredVideos.map(v => ({
      url: v.url.substring(0, 50) + '...',
      score: v.score,
      quality: v.quality
    })));

    // Identificar o vídeo principal (maior pontuação)
    const topVideo = scoredVideos[0];
    const mainVideoScore = topVideo.score;

    // Agrupar vídeos por "família" (mesmo vídeo, qualidades diferentes)
    const videoFamilies = this.groupVideosByFamily(scoredVideos);

    // Encontrar a família do vídeo principal
    const mainFamily = videoFamilies.find(family =>
      family.some(video => video.score === mainVideoScore)
    );

    if (mainFamily && mainFamily.length > 1) {
      console.log(`Encontradas ${mainFamily.length} qualidades do vídeo principal`);
      return mainFamily.sort((a, b) => b.score - a.score);
    }

    // Se não conseguir agrupar, retornar vídeos com pontuação similar
    const result = [topVideo];
    for (let i = 1; i < Math.min(5, scoredVideos.length); i++) {
      const video = scoredVideos[i];
      if (video.score >= mainVideoScore * 0.7) { // 70% da pontuação principal
        result.push(video);
      } else {
        break;
      }
    }

    console.log(`Selecionados ${result.length} vídeo(s) principal(is)`);
    return result;
  }

  // Agrupar vídeos por família (mesmo vídeo base, qualidades diferentes)
  static groupVideosByFamily(videos) {
    const families = [];
    const processed = new Set();

    for (const video of videos) {
      if (processed.has(video.url)) continue;

      const family = [video];
      processed.add(video.url);

      // Procurar vídeos similares (mesmo domínio, path similar)
      for (const otherVideo of videos) {
        if (processed.has(otherVideo.url)) continue;

        if (this.areVideosSimilar(video, otherVideo)) {
          family.push(otherVideo);
          processed.add(otherVideo.url);
        }
      }

      families.push(family);
    }

    return families.sort((a, b) => b.length - a.length); // Famílias maiores primeiro
  }

  // Verificar se dois vídeos são do mesmo conteúdo (qualidades diferentes)
  static areVideosSimilar(video1, video2) {
    try {
      const url1 = new URL(video1.url);
      const url2 = new URL(video2.url);

      // Mesmo domínio
      if (url1.hostname !== url2.hostname) return false;

      // Path similar (removendo parâmetros de qualidade)
      const path1 = url1.pathname.replace(/\d+p|_\d+|quality_\d+/g, '');
      const path2 = url2.pathname.replace(/\d+p|_\d+|quality_\d+/g, '');

      if (path1 === path2) return true;

      // URLs muito similares (diferença < 20%)
      const similarity = this.calculateUrlSimilarity(video1.url, video2.url);
      return similarity > 0.8;

    } catch {
      return false;
    }
  }

  // Calcular similaridade entre URLs
  static calculateUrlSimilarity(url1, url2) {
    const clean1 = url1.replace(/[?&](quality|resolution|format)=[^&]*/g, '');
    const clean2 = url2.replace(/[?&](quality|resolution|format)=[^&]*/g, '');

    const longer = clean1.length > clean2.length ? clean1 : clean2;
    const shorter = clean1.length > clean2.length ? clean2 : clean1;

    if (longer.length === 0) return 1.0;

    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  // Calcular distância de Levenshtein (similaridade de strings)
  static levenshteinDistance(str1, str2) {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  // Calcular pontuação para determinar se é o vídeo principal
  static calculateVideoScore(video, pageUrl) {
    let score = 0;
    const url = video.url.toLowerCase();
    const quality = video.quality?.toLowerCase() || '';
    const context = video.context?.toLowerCase() || '';

    console.log(`Calculando pontuação para: ${url.substring(0, 50)}...`);

    // Pontuação base por qualidade (mais peso para qualidades específicas)
    if (quality.includes('1080') || quality.includes('full hd')) {
      score += 60;
    } else if (quality.includes('720') || quality.includes('hd')) {
      score += 50;
    } else if (quality.includes('480')) {
      score += 35;
    } else if (quality.includes('360')) {
      score += 25;
    } else if (quality.includes('original')) {
      score += 55;
    } else if (quality.includes('auto') || quality.includes('best')) {
      score += 40;
    }

    // Pontuação por tamanho de arquivo (se disponível)
    if (video.filesize) {
      const sizeMB = video.filesize / (1024 * 1024);
      if (sizeMB > 200) score += 40;      // Vídeos muito grandes são principais
      else if (sizeMB > 100) score += 35;
      else if (sizeMB > 50) score += 25;
      else if (sizeMB > 10) score += 15;
      else if (sizeMB > 1) score += 5;
      else score -= 30;   // Vídeos muito pequenos são anúncios

      console.log(`Tamanho: ${sizeMB.toFixed(1)}MB, pontos: ${sizeMB > 200 ? 40 : sizeMB > 100 ? 35 : sizeMB > 50 ? 25 : sizeMB > 10 ? 15 : sizeMB > 1 ? 5 : -30}`);
    }

    // Pontuação por ter áudio
    if (video.hasAudio) {
      score += 30;
    }

    // Análise do contexto da página
    if (context) {
      // Bonificar se está em elementos principais
      const mainElements = [
        'main', 'article', 'content', 'player', 'video-player',
        'primary', 'hero', 'featured', 'spotlight'
      ];

      for (const element of mainElements) {
        if (context.includes(element)) {
          score += 25;
          console.log(`Contexto principal encontrado: ${element}`);
        }
      }

      // Penalizar se está em elementos secundários
      const secondaryElements = [
        'sidebar', 'aside', 'footer', 'header', 'nav', 'menu',
        'related', 'recommended', 'suggestions', 'ads', 'advertisement'
      ];

      for (const element of secondaryElements) {
        if (context.includes(element)) {
          score -= 20;
          console.log(`Contexto secundário encontrado: ${element}`);
        }
      }

      // Bonificar se tem indicadores de vídeo principal
      const primaryIndicators = [
        'data-main', 'data-primary', 'id="main', 'id="primary',
        'class="main', 'class="primary', 'featured', 'hero'
      ];

      for (const indicator of primaryIndicators) {
        if (context.includes(indicator)) {
          score += 35;
          console.log(`Indicador principal encontrado: ${indicator}`);
        }
      }
    }

    // Penalizar URLs que parecem ser anúncios ou conteúdo secundário
    const adKeywords = [
      'ad', 'ads', 'advertisement', 'promo', 'promotion', 'banner',
      'popup', 'overlay', 'preroll', 'midroll', 'postroll',
      'thumbnail', 'preview', 'teaser', 'related', 'recommended'
    ];

    for (const keyword of adKeywords) {
      if (url.includes(keyword)) {
        score -= 40;
        console.log(`Palavra-chave de anúncio encontrada: ${keyword}`);
      }
    }

    // Penalizar URLs muito curtas (provavelmente anúncios)
    if (url.length < 50) {
      score -= 20;
    }

    // Bonificar URLs que parecem ser conteúdo principal
    const mainKeywords = [
      'main', 'primary', 'content', 'video', 'stream', 'player',
      'watch', 'play', 'episode', 'movie', 'film', 'show'
    ];

    for (const keyword of mainKeywords) {
      if (url.includes(keyword)) {
        score += 20;
        console.log(`Palavra-chave principal encontrada: ${keyword}`);
      }
    }

    // Bonificar se a URL contém o domínio da página (conteúdo próprio vs terceiros)
    try {
      const pageHost = new URL(pageUrl).hostname;
      const videoHost = new URL(video.url).hostname;
      if (pageHost === videoHost) {
        score += 25;
        console.log('Vídeo do mesmo domínio da página');
      } else {
        // Penalizar vídeos de domínios de anúncios conhecidos
        const adDomains = ['doubleclick', 'googlesyndication', 'googleadservices', 'facebook.com/tr'];
        if (adDomains.some(domain => videoHost.includes(domain))) {
          score -= 50;
          console.log('Domínio de anúncio detectado');
        }
      }
    } catch (e) {
      // Ignorar erros de URL
    }

    // Bonificar formatos de vídeo mais comuns para conteúdo principal
    if (url.includes('.mp4')) {
      score += 15;
    } else if (url.includes('.webm')) {
      score += 10;
    } else if (url.includes('.m3u8')) {
      score += 20; // HLS streams são geralmente conteúdo principal
    }

    // Bonificar vídeos que aparecem primeiro na página (posição no HTML)
    if (video.position && video.position < 3) {
      score += 30; // Primeiros vídeos na página têm prioridade
    }

    const finalScore = Math.max(0, score);
    console.log(`Pontuação final: ${finalScore}`);
    return finalScore;
  }
  
  // Verificar se uma URL de vídeo é acessível
  static async isVideoAccessible(videoUrl) {
    try {
      const response = await fetch(videoUrl, { 
        method: 'HEAD',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      const contentType = response.headers.get('content-type');
      const contentLength = response.headers.get('content-length');
      
      return response.ok && (
        (contentType && contentType.includes('video')) ||
        this.isValidVideoUrl(videoUrl) ||
        (contentLength && parseInt(contentLength) > 50000)
      );
    } catch {
      return false;
    }
  }
  
  // Obter a melhor qualidade disponível
  static getBestQuality(videoUrls) {
    if (!videoUrls || videoUrls.length === 0) return null;
    
    // Priorizar vídeos com áudio
    const withAudio = videoUrls.filter(v => v.hasAudio);
    const toSort = withAudio.length > 0 ? withAudio : videoUrls;
    
    // Ordenar por qualidade (assumindo que qualidades maiores são melhores)
    return toSort.sort((a, b) => {
      const aHeight = parseInt(a.quality) || 0;
      const bHeight = parseInt(b.quality) || 0;
      return bHeight - aHeight;
    })[0];
  }
}

export default VideoExtractorService;
