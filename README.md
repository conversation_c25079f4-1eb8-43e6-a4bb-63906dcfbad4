# Video Downloader App

Um aplicativo Android simples e eficiente para baixar vídeos de diversos sites através de links.

## 🚀 Características

- **Interface Simples**: Campo para colar o link e botão de download com ícone de seta
- **Validação Inteligente**: Validação em tempo real de URLs com feedback visual
- **Múltiplos Sites**: Suporte para YouTube, Vimeo, TikTok, Instagram, Facebook, Twitter e links diretos
- **Download Avançado**: Sistema inteligente que tenta extrair URLs diretas de vídeo
- **Progresso Visual**: Indicador de progresso com porcentagem e status
- **Galeria Integrada**: Vídeos salvos automaticamente na galeria do dispositivo
- **Gerenciamento de Permissões**: Sistema amigável para solicitar permissões necessárias

## 📱 Sites Suportados

- YouTube (youtube.com, youtu.be)
- Vimeo (vimeo.com)
- TikTok (tiktok.com)
- Instagram (instagram.com)
- Facebook (facebook.com, fb.watch)
- Twitter/X (twitter.com, x.com)
- Links diretos de vídeo (.mp4, .avi, .mov, etc.)

## 🛠️ Instalação

### Pré-requisitos

1. **Node.js** (versão 16 ou superior)
2. **Expo CLI**
3. **Android Studio** (para emulador) ou dispositivo Android

### Passos de Instalação

1. **Clone ou baixe o projeto**
```bash
cd video-downloader-app
```

2. **Instale as dependências**
```bash
npm install
```

3. **Instale o Expo CLI globalmente** (se não tiver)
```bash
npm install -g @expo/cli
```

4. **Inicie o projeto**
```bash
npx expo start
```

5. **Execute no dispositivo**
   - Escaneie o QR code com o app Expo Go
   - Ou pressione 'a' para abrir no emulador Android

## 📦 Build para Produção

### Build APK

1. **Configure o EAS Build**
```bash
npm install -g @expo/eas-cli
eas login
eas build:configure
```

2. **Gere o APK**
```bash
eas build --platform android --profile preview
```

### Build para Google Play Store

```bash
eas build --platform android --profile production
```

## 🎯 Como Usar

1. **Abra o aplicativo**
2. **Cole o link do vídeo** no campo de texto
3. **Aguarde a validação** (ícone verde = válido, vermelho = inválido)
4. **Toque no botão de download** (seta para baixo)
5. **Conceda as permissões** quando solicitado
6. **Aguarde o download** (progresso será mostrado)
7. **Vídeo salvo** na galeria automaticamente

## 🔧 Estrutura do Projeto

```
video-downloader-app/
├── App.js                          # Componente principal
├── services/
│   └── VideoDownloadService.js     # Serviço de download avançado
├── utils/
│   ├── UrlValidator.js             # Validador de URLs
│   └── PermissionManager.js        # Gerenciador de permissões
├── android/
│   └── app/src/main/
│       ├── AndroidManifest.xml     # Configurações Android
│       └── res/xml/file_paths.xml  # Configuração de arquivos
├── package.json                    # Dependências do projeto
├── app.json                        # Configuração do Expo
└── babel.config.js                 # Configuração do Babel
```

## 🔒 Permissões Necessárias

- **INTERNET**: Para baixar vídeos
- **WRITE_EXTERNAL_STORAGE**: Para salvar arquivos
- **READ_EXTERNAL_STORAGE**: Para acessar arquivos salvos
- **READ_MEDIA_VIDEO**: Para Android 10+ (API 29+)

## 🐛 Solução de Problemas

### Erro de Permissões
- Vá em Configurações > Aplicativos > Video Downloader > Permissões
- Ative todas as permissões necessárias

### Link não funciona
- Tente copiar o link diretamente do navegador
- Alguns vídeos podem estar protegidos por direitos autorais
- Verifique se o link contém um vídeo válido

### Download falha
- Verifique sua conexão com a internet
- Certifique-se de ter espaço suficiente no dispositivo
- Alguns sites podem bloquear downloads automatizados

## 📝 Limitações

- Alguns sites podem ter proteções anti-bot
- Vídeos com DRM não podem ser baixados
- Links privados ou que exigem login podem não funcionar
- Qualidade do vídeo depende do link original

## 🔄 Atualizações Futuras

- [ ] Suporte para mais sites
- [ ] Seleção de qualidade de vídeo
- [ ] Download de playlists
- [ ] Histórico de downloads
- [ ] Modo escuro/claro
- [ ] Compartilhamento direto de outros apps

## 📄 Licença

Este projeto é para fins educacionais. Respeite os direitos autorais e termos de uso dos sites de origem.

## ⚠️ Aviso Legal

Este aplicativo é fornecido "como está" para fins educacionais. Os usuários são responsáveis por:
- Respeitar direitos autorais
- Seguir termos de uso dos sites
- Usar apenas para conteúdo próprio ou com permissão

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor:
1. Faça um fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Abra um Pull Request

## 📞 Suporte

Para problemas ou sugestões, abra uma issue no repositório do projeto.
