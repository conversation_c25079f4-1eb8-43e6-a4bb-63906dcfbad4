{"version": 3, "names": ["React", "I18nManager", "Platform", "StyleSheet", "View", "Pager", "SceneView", "TabBar", "jsx", "_jsx", "jsxs", "_jsxs", "renderLazyPlaceholderDefault", "TabView", "onIndexChange", "onTabSelect", "navigationState", "renderScene", "initialLayout", "keyboardDismissMode", "lazy", "lazyPreloadDistance", "onSwipeStart", "onSwipeEnd", "renderLazyPlaceholder", "renderTabBar", "props", "pagerStyle", "style", "direction", "getConstants", "isRTL", "swipeEnabled", "tabBarPosition", "animationEnabled", "overScrollMode", "options", "sceneOptions", "commonOptions", "OS", "console", "warn", "layout", "setLayout", "useState", "width", "height", "jumpToIndex", "index", "handleLayout", "e", "nativeEvent", "prevLayout", "Object", "fromEntries", "routes", "map", "route", "key", "onLayout", "styles", "pager", "children", "layoutDirection", "position", "render", "addEnterListener", "jumpTo", "sceneRendererProps", "Fragment", "i", "sceneStyle", "loading", "create", "flex", "overflow"], "sourceRoot": "../../src", "sources": ["TabView.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,WAAW,EAEXC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AAErB,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,SAAS,QAAQ,gBAAa;AACvC,SAASC,MAAM,QAAQ,aAAU;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAkClC,MAAMC,4BAA4B,GAAGA,CAAA,KAAM,IAAI;AAE/C,OAAO,SAASC,OAAOA,CAAkB;EACvCC,aAAa;EACbC,WAAW;EACXC,eAAe;EACfC,WAAW;EACXC,aAAa;EACbC,mBAAmB,GAAG,MAAM;EAC5BC,IAAI,GAAG,KAAK;EACZC,mBAAmB,GAAG,CAAC;EACvBC,YAAY;EACZC,UAAU;EACVC,qBAAqB,GAAGZ,4BAA4B;EACpD;EACAa,YAAY,GAAIC,KAAK,iBAAKjB,IAAA,CAACF,MAAM;IAAA,GAAKmB;EAAK,CAAG,CAAC;EAC/CC,UAAU;EACVC,KAAK;EACLC,SAAS,GAAG5B,WAAW,CAAC6B,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAC5DC,YAAY,GAAG,IAAI;EACnBC,cAAc,GAAG,KAAK;EACtBC,gBAAgB,GAAG,IAAI;EACvBC,cAAc;EACdC,OAAO,EAAEC,YAAY;EACrBC;AACQ,CAAC,EAAE;EACX,IACEpC,QAAQ,CAACqC,EAAE,KAAK,KAAK,IACrBV,SAAS,MAAM5B,WAAW,CAAC6B,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,EAChE;IACAS,OAAO,CAACC,IAAI,CACV,mCAAmCZ,SAAS,iCAC1C5B,WAAW,CAAC6B,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK,0GAEpD,CAAC;EACH;EAEA,MAAM,CAACW,MAAM,EAAEC,SAAS,CAAC,GAAG3C,KAAK,CAAC4C,QAAQ,CAAC;IACzCC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACT,GAAG5B;EACL,CAAC,CAAC;EAEF,MAAM6B,WAAW,GAAIC,KAAa,IAAK;IACrC,IAAIA,KAAK,KAAKhC,eAAe,CAACgC,KAAK,EAAE;MACnClC,aAAa,CAACkC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAoB,IAAK;IAC7C,MAAM;MAAEJ,MAAM;MAAED;IAAM,CAAC,GAAGK,CAAC,CAACC,WAAW,CAACT,MAAM;IAE9CC,SAAS,CAAES,UAAU,IAAK;MACxB,IAAIA,UAAU,CAACP,KAAK,KAAKA,KAAK,IAAIO,UAAU,CAACN,MAAM,KAAKA,MAAM,EAAE;QAC9D,OAAOM,UAAU;MACnB;MAEA,OAAO;QAAEN,MAAM;QAAED;MAAM,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMT,OAAO,GAAGiB,MAAM,CAACC,WAAW,CAChCtC,eAAe,CAACuC,MAAM,CAACC,GAAG,CAAEC,KAAK,IAAK,CACpCA,KAAK,CAACC,GAAG,EACT;IACE,GAAGpB,aAAa;IAChB,GAAGD,YAAY,GAAGoB,KAAK,CAACC,GAAG;EAC7B,CAAC,CACF,CACH,CAAC;EAED,oBACEjD,IAAA,CAACL,IAAI;IAACuD,QAAQ,EAAEV,YAAa;IAACrB,KAAK,EAAE,CAACgC,MAAM,CAACC,KAAK,EAAEjC,KAAK,CAAE;IAAAkC,QAAA,eACzDrD,IAAA,CAACJ,KAAK;MACJqC,MAAM,EAAEA,MAAO;MACf1B,eAAe,EAAEA,eAAgB;MACjCG,mBAAmB,EAAEA,mBAAoB;MACzCa,YAAY,EAAEA,YAAa;MAC3BV,YAAY,EAAEA,YAAa;MAC3BC,UAAU,EAAEA,UAAW;MACvBT,aAAa,EAAEiC,WAAY;MAC3BhC,WAAW,EAAEA,WAAY;MACzBmB,gBAAgB,EAAEA,gBAAiB;MACnCC,cAAc,EAAEA,cAAe;MAC/BP,KAAK,EAAED,UAAW;MAClBoC,eAAe,EAAElC,SAAU;MAAAiC,QAAA,EAE1BA,CAAC;QAAEE,QAAQ;QAAEC,MAAM;QAAEC,gBAAgB;QAAEC;MAAO,CAAC,KAAK;QACnD;QACA;QACA,MAAMC,kBAAkB,GAAG;UACzBJ,QAAQ;UACRtB,MAAM;UACNyB;QACF,CAAC;QAED,oBACExD,KAAA,CAACX,KAAK,CAACqE,QAAQ;UAAAP,QAAA,GACZ7B,cAAc,KAAK,KAAK,IACvBR,YAAY,CAAC;YACX,GAAG2C,kBAAkB;YACrBhC,OAAO;YACPpB;UACF,CAAC,CAAC,EACHiD,MAAM,CACLjD,eAAe,CAACuC,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEa,CAAC,KAAK;YACvC,MAAM;cAAEC;YAAW,CAAC,GAAGnC,OAAO,GAAGqB,KAAK,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEjD,oBACEjD,IAAA,CAACH,SAAS;cAAA,GAEJ8D,kBAAkB;cACtBF,gBAAgB,EAAEA,gBAAiB;cACnClB,KAAK,EAAEsB,CAAE;cACTlD,IAAI,EAAE,OAAOA,IAAI,KAAK,UAAU,GAAGA,IAAI,CAAC;gBAAEqC;cAAM,CAAC,CAAC,GAAGrC,IAAK;cAC1DC,mBAAmB,EAAEA,mBAAoB;cACzCL,eAAe,EAAEA,eAAgB;cACjCY,KAAK,EAAE2C,UAAW;cAAAT,QAAA,EAEjBA,CAAC;gBAAEU;cAAQ,CAAC,KACXA,OAAO,GACHhD,qBAAqB,CAAC;gBAAEiC;cAAM,CAAC,CAAC,GAChCxC,WAAW,CAAC;gBACV,GAAGmD,kBAAkB;gBACrBX;cACF,CAAC;YAAC,GAfHA,KAAK,CAACC,GAiBF,CAAC;UAEhB,CAAC,CACH,CAAC,EACAzB,cAAc,KAAK,QAAQ,IAC1BR,YAAY,CAAC;YACX,GAAG2C,kBAAkB;YACrBhC,OAAO;YACPpB;UACF,CAAC,CAAC;QAAA,CACU,CAAC;MAErB;IAAC,CACI;EAAC,CACJ,CAAC;AAEX;AAEA,MAAM4C,MAAM,GAAGzD,UAAU,CAACsE,MAAM,CAAC;EAC/BZ,KAAK,EAAE;IACLa,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}