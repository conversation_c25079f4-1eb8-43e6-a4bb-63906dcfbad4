var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireWildcard(require("react"));var _reactNative=require("react-native");var _codegenNativeCommands=_interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeCommands"));var _invariant=_interopRequireDefault(require("invariant"));var _WebViewNativeComponent=require("./WebViewNativeComponent.windows");var _WebViewShared=require("./WebViewShared");var _WebView=_interopRequireDefault(require("./WebView.styles"));var _jsxRuntime=require("react/jsx-runtime");var _excluded=["cacheEnabled","originWhitelist","startInLoadingState","onNavigationStateChange","onLoadStart","onError","onLoad","onLoadEnd","onLoadProgress","onOpenWindow","onSourceChanged","onHttpError","onMessage","renderLoading","renderError","style","containerStyle","source","nativeConfig","onShouldStartLoadWithRequest","useWebView2"];var _this=this,_jsxFileName="/home/<USER>/code/src/WebView.windows.tsx";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&Object.prototype.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}var Commands=(0,_codegenNativeCommands.default)({supportedCommands:['goBack','goForward','reload','stopLoading','injectJavaScript','requestFocus','clearCache','postMessage','loadUrl']});var resolveAssetSource=_reactNative.Image.resolveAssetSource;var WebViewComponent=(0,_react.forwardRef)(function(_ref,ref){var _ref$cacheEnabled=_ref.cacheEnabled,cacheEnabled=_ref$cacheEnabled===void 0?true:_ref$cacheEnabled,_ref$originWhitelist=_ref.originWhitelist,originWhitelist=_ref$originWhitelist===void 0?_WebViewShared.defaultOriginWhitelist:_ref$originWhitelist,startInLoadingState=_ref.startInLoadingState,onNavigationStateChange=_ref.onNavigationStateChange,onLoadStart=_ref.onLoadStart,onError=_ref.onError,onLoad=_ref.onLoad,onLoadEnd=_ref.onLoadEnd,onLoadProgress=_ref.onLoadProgress,onOpenWindowProp=_ref.onOpenWindow,onSourceChanged=_ref.onSourceChanged,onHttpErrorProp=_ref.onHttpError,onMessageProp=_ref.onMessage,renderLoading=_ref.renderLoading,renderError=_ref.renderError,style=_ref.style,containerStyle=_ref.containerStyle,source=_ref.source,nativeConfig=_ref.nativeConfig,onShouldStartLoadWithRequestProp=_ref.onShouldStartLoadWithRequest,useWebView2=_ref.useWebView2,otherProps=(0,_objectWithoutProperties2.default)(_ref,_excluded);var webViewRef=(0,_react.useRef)(null);var RCTWebViewString=useWebView2?'RCTWebView2':'RCTWebView';var onShouldStartLoadWithRequestCallback=(0,_react.useCallback)(function(shouldStart,url,lockIdentifier){if(lockIdentifier){if(RCTWebViewString==='RCTWebView'){_reactNative.NativeModules.RCTWebView.onShouldStartLoadWithRequestCallback(shouldStart,lockIdentifier);}else{_reactNative.NativeModules.RCTWebView2.onShouldStartLoadWithRequestCallback(shouldStart,lockIdentifier);}}else if(shouldStart){Commands.loadUrl(webViewRef,url);}},[RCTWebViewString]);var _useWebViewLogic=(0,_WebViewShared.useWebViewLogic)({onNavigationStateChange:onNavigationStateChange,onLoad:onLoad,onError:onError,onHttpErrorProp:onHttpErrorProp,onLoadEnd:onLoadEnd,onLoadProgress:onLoadProgress,onLoadStart:onLoadStart,onMessageProp:onMessageProp,startInLoadingState:startInLoadingState,originWhitelist:originWhitelist,onShouldStartLoadWithRequestProp:onShouldStartLoadWithRequestProp,onShouldStartLoadWithRequestCallback:onShouldStartLoadWithRequestCallback,onOpenWindowProp:onOpenWindowProp}),onLoadingStart=_useWebViewLogic.onLoadingStart,onShouldStartLoadWithRequest=_useWebViewLogic.onShouldStartLoadWithRequest,onMessage=_useWebViewLogic.onMessage,viewState=_useWebViewLogic.viewState,setViewState=_useWebViewLogic.setViewState,lastErrorEvent=_useWebViewLogic.lastErrorEvent,onHttpError=_useWebViewLogic.onHttpError,onLoadingError=_useWebViewLogic.onLoadingError,onLoadingFinish=_useWebViewLogic.onLoadingFinish,onLoadingProgress=_useWebViewLogic.onLoadingProgress,onOpenWindow=_useWebViewLogic.onOpenWindow;(0,_react.useImperativeHandle)(ref,function(){return{goForward:function goForward(){return Commands.goForward(webViewRef.current);},goBack:function goBack(){return Commands.goBack(webViewRef.current);},reload:function reload(){setViewState('LOADING');Commands.reload(webViewRef.current);},stopLoading:function stopLoading(){return Commands.stopLoading(webViewRef.current);},postMessage:function postMessage(data){return Commands.postMessage(webViewRef.current,data);},injectJavaScript:function injectJavaScript(data){return Commands.injectJavaScript(webViewRef.current,data);},requestFocus:function requestFocus(){return Commands.requestFocus(webViewRef.current);},clearCache:function clearCache(){return Commands.clearCache(webViewRef.current);},loadUrl:function loadUrl(url){return Commands.loadUrl(webViewRef.current,url);}};},[setViewState,webViewRef]);var otherView=null;if(viewState==='LOADING'){otherView=(renderLoading||_WebViewShared.defaultRenderLoading)();}else if(viewState==='ERROR'){(0,_invariant.default)(lastErrorEvent!=null,'lastErrorEvent expected to be non-null');otherView=(renderError||_WebViewShared.defaultRenderError)(lastErrorEvent.domain,lastErrorEvent.code,lastErrorEvent.description);}else if(viewState!=='IDLE'){console.error(`RNCWebView invalid state encountered: ${viewState}`);}var webViewStyles=[_WebView.default.container,_WebView.default.webView,style];var webViewContainerStyle=[_WebView.default.container,containerStyle];var NativeWebView=useWebView2?_WebViewNativeComponent.RCTWebView2:_WebViewNativeComponent.RCTWebView;var webView=(0,_jsxRuntime.jsx)(NativeWebView,Object.assign({},otherProps,{messagingEnabled:typeof onMessageProp==='function',linkHandlingEnabled:typeof onOpenWindowProp==='function',onLoadingError:onLoadingError,onLoadingFinish:onLoadingFinish,onLoadingProgress:onLoadingProgress,onLoadingStart:onLoadingStart,onHttpError:onHttpError,onMessage:onMessage,onShouldStartLoadWithRequest:onShouldStartLoadWithRequest,onOpenWindow:onOpenWindow,onSourceChanged:onSourceChanged,ref:webViewRef,source:resolveAssetSource(source),style:webViewStyles,cacheEnabled:cacheEnabled},nativeConfig==null?void 0:nativeConfig.props),"webViewKey");return(0,_jsxRuntime.jsxs)(_reactNative.View,{style:webViewContainerStyle,children:[webView,otherView]});});var isFileUploadSupported=function(){var _ref2=(0,_asyncToGenerator2.default)(function*(){return false;});return function isFileUploadSupported(){return _ref2.apply(this,arguments);};}();var WebView=Object.assign(WebViewComponent,{isFileUploadSupported:isFileUploadSupported});var _default=exports.default=WebView;