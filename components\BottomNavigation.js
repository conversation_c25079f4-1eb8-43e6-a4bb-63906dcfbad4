import React from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

const BottomNavigation = ({ 
  activeTab, 
  onTabPress,
  downloadCount = 0,
  bookmarkCount = 0 
}) => {
  const tabs = [
    {
      id: 'browser',
      icon: 'globe-outline',
      activeIcon: 'globe',
      label: 'Navegador',
      badge: null
    },
    {
      id: 'downloads',
      icon: 'download-outline',
      activeIcon: 'download',
      label: 'Downloads',
      badge: downloadCount > 0 ? downloadCount : null
    },
    {
      id: 'bookmarks',
      icon: 'bookmarks-outline',
      activeIcon: 'bookmarks',
      label: 'Favoritos',
      badge: bookmarkCount > 0 ? bookmarkCount : null
    },
    {
      id: 'settings',
      icon: 'settings-outline',
      activeIcon: 'settings',
      label: 'Configurações',
      badge: null
    }
  ];

  return (
    <View style={styles.container}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        
        return (
          <TouchableOpacity
            key={tab.id}
            style={styles.tabButton}
            onPress={() => onTabPress(tab.id)}
            activeOpacity={0.7}
          >
            <View style={styles.iconContainer}>
              <Ionicons
                name={isActive ? tab.activeIcon : tab.icon}
                size={24}
                color={isActive ? '#6c5ce7' : '#888'}
              />
              
              {tab.badge && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>
                    {tab.badge > 99 ? '99+' : tab.badge}
                  </Text>
                </View>
              )}
            </View>
            
            <Text style={[
              styles.tabLabel,
              isActive && styles.activeTabLabel
            ]}>
              {tab.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#1a1a2e',
    borderTopWidth: 1,
    borderTopColor: '#2d2d44',
    paddingBottom: 5,
    paddingTop: 8,
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 5,
  },
  iconContainer: {
    position: 'relative',
    marginBottom: 4,
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#ff4444',
    borderRadius: 10,
    minWidth: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  tabLabel: {
    color: '#888',
    fontSize: 10,
    textAlign: 'center',
  },
  activeTabLabel: {
    color: '#6c5ce7',
    fontWeight: 'bold',
  },
});

export default BottomNavigation;
