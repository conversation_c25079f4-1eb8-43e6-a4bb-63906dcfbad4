{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_reactNative", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "childrenWithOverriddenStyle", "children", "Children", "map", "child", "element", "createElement", "View", "style", "StyleSheet", "absoluteFill", "collapsable", "React", "cloneElement", "props", "exports"], "sources": ["utils.tsx"], "sourcesContent": ["import React, { Children, ReactNode } from 'react';\nimport { StyleSheet, View } from 'react-native';\n\nexport const childrenWithOverriddenStyle = (children?: ReactNode) => {\n  return Children.map(children, (child) => {\n    const element = child as React.ReactElement<any>;\n    return (\n      // Add a wrapper to ensure layout is calculated correctly\n      <View style={StyleSheet.absoluteFill} collapsable={false}>\n        {React.cloneElement(element, {\n          ...element.props,\n          // Override styles so that each page will fill the parent.\n          style: [element.props.style, StyleSheet.absoluteFill],\n        })}\n      </View>\n    );\n  });\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAgD,SAAAE,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEzC,MAAMW,2BAA2B,GAAIC,QAAoB,IAAK;EACnE,OAAOC,eAAQ,CAACC,GAAG,CAACF,QAAQ,EAAGG,KAAK,IAAK;IACvC,MAAMC,OAAO,GAAGD,KAAgC;IAChD;MAAA;MACE;MACA5B,MAAA,CAAAU,OAAA,CAAAoB,aAAA,CAAC3B,YAAA,CAAA4B,IAAI;QAACC,KAAK,EAAEC,uBAAU,CAACC,YAAa;QAACC,WAAW,EAAE;MAAM,gBACtDC,cAAK,CAACC,YAAY,CAACR,OAAO,EAAE;QAC3B,GAAGA,OAAO,CAACS,KAAK;QAChB;QACAN,KAAK,EAAE,CAACH,OAAO,CAACS,KAAK,CAACN,KAAK,EAAEC,uBAAU,CAACC,YAAY;MACtD,CAAC,CACG;IAAC;EAEX,CAAC,CAAC;AACJ,CAAC;AAACK,OAAA,CAAAf,2BAAA,GAAAA,2BAAA", "ignoreList": []}