import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Alert,
  StatusBar,
  Dimensions,
  Text,
} from 'react-native';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import Constants from 'expo-constants';
import VideoDownloadService from './services/VideoDownloadService';
import UrlValidator from './utils/UrlValidator';
import PermissionManager from './utils/PermissionManager';
import TabBrowser from './components/Browser/TabBrowser';
import BottomNavigation from './components/BottomNavigation';
import DownloadManager from './components/DownloadManager';
import Settings from './components/Settings';
import VideoQualitySelector from './components/VideoQualitySelector';

const { width, height } = Dimensions.get('window');

export default function App() {
  const [activeTab, setActiveTab] = useState('browser');
  const [showDownloadManager, setShowDownloadManager] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showQualitySelector, setShowQualitySelector] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState('');
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [downloadStatus, setDownloadStatus] = useState('');
  const [activeDownloads, setActiveDownloads] = useState(new Set()); // Controle de downloads duplicados

  // Variáveis para compatibilidade (não usadas no novo design)
  const [url, setUrl] = useState('');
  const [urlValidation, setUrlValidation] = useState(null);

  useEffect(() => {
    // Limpar arquivos temporários na inicialização
    VideoDownloadService.cleanupTempFiles();
  }, []);

  const requestPermissions = async () => {
    const result = await PermissionManager.ensurePermissions();

    if (!result.success) {
      if (!result.cancelled) {
        Alert.alert('Erro', result.message);
      }
      return false;
    }

    return true;
  };

  const handleTabPress = (tabId) => {
    if (tabId === 'downloads') {
      setShowDownloadManager(true);
    } else if (tabId === 'settings') {
      setShowSettings(true);
    } else {
      setActiveTab(tabId);
    }
  };

  const handleVideoDetected = (url) => {
    console.log('Vídeo detectado:', url);
    // Pode mostrar uma notificação ou indicador visual
  };

  const handleDownloadRequest = (url) => {
    setCurrentVideoUrl(url);
    setShowQualitySelector(true);
  };

  const handleQualitySelected = async (quality) => {
    // Verificar se já está baixando este vídeo
    const downloadKey = `${currentVideoUrl}_${quality.quality}`;
    if (activeDownloads.has(downloadKey)) {
      Alert.alert('Download em Andamento', 'Este vídeo já está sendo baixado. Aguarde a conclusão.');
      return;
    }

    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    // Adicionar ao controle de downloads ativos
    setActiveDownloads(prev => new Set([...prev, downloadKey]));

    setIsDownloading(true);
    setDownloadProgress(0);
    setDownloadStatus('Preparando download...');

    try {
      console.log('Iniciando download com qualidade:', quality);

      // Callback para progresso do download
      const progressCallback = (downloadProgress) => {
        const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
        setDownloadProgress(Math.round(progress * 100));
        setDownloadStatus(`Baixando vídeo... ${Math.round(progress * 100)}%`);
      };

      // Verificar se temos uma URL real de vídeo
      const downloadUrl = quality.url;
      if (!downloadUrl || downloadUrl === currentVideoUrl) {
        throw new Error('URL de vídeo não encontrada. Tente selecionar outra qualidade.');
      }

      console.log('URL de download:', downloadUrl.substring(0, 100) + '...');

      setDownloadStatus('Iniciando download...');
      const result = await VideoDownloadService.downloadVideo(downloadUrl, progressCallback);

      console.log('Download concluído:', result);

      setDownloadStatus('Salvando na galeria...');
      await VideoDownloadService.saveToGallery(result.uri, result.fileName);

      // Adicionar ao gerenciador de downloads
      await DownloadManager.addDownload({
        ...result,
        quality: quality,
        originalUrl: currentVideoUrl
      });

      Alert.alert(
        'Sucesso!',
        `Vídeo baixado com sucesso!\n\nArquivo: ${result.fileName}\nQualidade: ${quality.label}\nTamanho: ${(result.size / 1024 / 1024).toFixed(2)} MB\n\nSalvo na galeria do dispositivo.`,
        [{ text: 'OK' }]
      );

    } catch (error) {
      console.error('Erro no download:', error);

      let errorMessage = 'Não foi possível baixar o vídeo. ';

      if (error.message.includes('URL de vídeo não encontrada')) {
        errorMessage += 'Não foi possível extrair a URL real do vídeo. Tente outro vídeo ou qualidade.';
      } else if (error.message.includes('muito pequeno')) {
        errorMessage += 'O arquivo baixado é muito pequeno - pode não ser o vídeo real. Tente outra qualidade.';
      } else if (error.message.includes('não acessível')) {
        errorMessage += 'A URL do vídeo não está acessível. Tente outra qualidade.';
      } else if (error.message.includes('não é um arquivo de vídeo')) {
        errorMessage += 'O link não aponta para um arquivo de vídeo válido.';
      } else if (error.message.includes('galeria')) {
        errorMessage += 'Erro ao salvar na galeria.';
      } else {
        errorMessage += `Detalhes: ${error.message}\n\nTente:\n• Selecionar outra qualidade\n• Verificar conexão\n• Tentar outro vídeo`;
      }

      Alert.alert('Erro no Download', errorMessage);
    } finally {
      // Remover do controle de downloads ativos
      setActiveDownloads(prev => {
        const newSet = new Set(prev);
        newSet.delete(downloadKey);
        return newSet;
      });

      setIsDownloading(false);
      setDownloadProgress(0);
      setDownloadStatus('');
    }
  };

  return (
    <SafeAreaProvider>
      <SafeAreaView style={styles.container} edges={['top']}>
        <StatusBar
          barStyle="light-content"
          backgroundColor="#1a1a2e"
          translucent={false}
        />

        {/* Conteúdo principal baseado na aba ativa */}
        {activeTab === 'browser' && (
          <TabBrowser
            onVideoDetected={handleVideoDetected}
            onDownloadRequest={handleDownloadRequest}
          />
        )}

        {activeTab === 'bookmarks' && (
          <View style={styles.placeholderContainer}>
            <Text style={styles.placeholderText}>Favoritos em desenvolvimento</Text>
          </View>
        )}

        {/* Menu inferior de navegação */}
        <SafeAreaView edges={['bottom']} style={styles.bottomNavContainer}>
          <BottomNavigation
            activeTab={activeTab}
            onTabPress={handleTabPress}
            downloadCount={0} // TODO: implementar contagem real
            bookmarkCount={0} // TODO: implementar contagem real
          />
        </SafeAreaView>

        {/* Modais */}
        <DownloadManager
          visible={showDownloadManager}
          onClose={() => setShowDownloadManager(false)}
        />

        <Settings
          visible={showSettings}
          onClose={() => setShowSettings(false)}
        />

        <VideoQualitySelector
          visible={showQualitySelector}
          onClose={() => setShowQualitySelector(false)}
          videoUrl={currentVideoUrl}
          onQualitySelected={handleQualitySelected}
          onDownloadStart={handleQualitySelected}
        />

        {/* Indicador de download em progresso */}
        {isDownloading && (
          <View style={styles.downloadOverlay}>
            <View style={styles.downloadModal}>
              <Text style={styles.downloadTitle}>Download em Progresso</Text>
              <View style={styles.progressBarContainer}>
                <View
                  style={[styles.progressBar, { width: `${downloadProgress}%` }]}
                />
              </View>
              <Text style={styles.downloadStatus}>{downloadStatus}</Text>
              <Text style={styles.downloadProgress}>{downloadProgress}%</Text>
            </View>
          </View>
        )}
      </SafeAreaView>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  bottomNavContainer: {
    backgroundColor: '#1a1a2e',
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a2e',
  },
  placeholderText: {
    color: '#888',
    fontSize: 16,
  },
  downloadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  downloadModal: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    padding: 30,
    width: width * 0.8,
    alignItems: 'center',
  },
  downloadTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: '#2d2d44',
    borderRadius: 4,
    marginBottom: 15,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#6c5ce7',
    borderRadius: 4,
  },
  downloadStatus: {
    color: '#888',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 10,
  },
  downloadProgress: {
    color: '#6c5ce7',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
