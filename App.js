import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Alert,
  StatusBar,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import VideoDownloadService from './services/VideoDownloadService';
import UrlValidator from './utils/UrlValidator';
import PermissionManager from './utils/PermissionManager';
import TabBrowser from './components/Browser/TabBrowser';
import BottomNavigation from './components/BottomNavigation';
import DownloadManager from './components/DownloadManager';
import Settings from './components/Settings';
import VideoQualitySelector from './components/VideoQualitySelector';

const { width, height } = Dimensions.get('window');

export default function App() {
  const [activeTab, setActiveTab] = useState('browser');
  const [showDownloadManager, setShowDownloadManager] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showQualitySelector, setShowQualitySelector] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState('');
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [downloadStatus, setDownloadStatus] = useState('');

  // Variáveis para compatibilidade (não usadas no novo design)
  const [url, setUrl] = useState('');
  const [urlValidation, setUrlValidation] = useState(null);

  useEffect(() => {
    // Limpar arquivos temporários na inicialização
    VideoDownloadService.cleanupTempFiles();
  }, []);

  const requestPermissions = async () => {
    const result = await PermissionManager.ensurePermissions();

    if (!result.success) {
      if (!result.cancelled) {
        Alert.alert('Erro', result.message);
      }
      return false;
    }

    return true;
  };

  const handleTabPress = (tabId) => {
    if (tabId === 'downloads') {
      setShowDownloadManager(true);
    } else if (tabId === 'settings') {
      setShowSettings(true);
    } else {
      setActiveTab(tabId);
    }
  };

  const handleVideoDetected = (url) => {
    console.log('Vídeo detectado:', url);
    // Pode mostrar uma notificação ou indicador visual
  };

  const handleDownloadRequest = (url) => {
    setCurrentVideoUrl(url);
    setShowQualitySelector(true);
  };

  const handleQualitySelected = async (quality) => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setIsDownloading(true);
    setDownloadProgress(0);
    setDownloadStatus('Analisando URL...');

    try {
      // Callback para progresso do download
      const progressCallback = (downloadProgress) => {
        const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
        setDownloadProgress(Math.round(progress * 100));
        setDownloadStatus('Baixando vídeo...');
      };

      // Usar o serviço de download avançado
      setDownloadStatus('Processando link...');
      const result = await VideoDownloadService.downloadVideo(quality.url, progressCallback);

      setDownloadStatus('Salvando na galeria...');
      await VideoDownloadService.saveToGallery(result.uri, result.fileName);

      // Adicionar ao gerenciador de downloads
      await DownloadManager.addDownload({
        ...result,
        quality: quality,
        originalUrl: currentVideoUrl
      });

      Alert.alert(
        'Sucesso!',
        `Vídeo baixado com sucesso!\n\nArquivo: ${result.fileName}\nQualidade: ${quality.label}\nTamanho: ${(result.size / 1024 / 1024).toFixed(2)} MB\n\nSalvo na galeria do dispositivo.`,
        [{ text: 'OK' }]
      );

    } catch (error) {
      console.error('Erro no download:', error);

      let errorMessage = 'Não foi possível baixar o vídeo. ';

      if (error.message.includes('URL inválida')) {
        errorMessage += 'Verifique se o link está correto.';
      } else if (error.message.includes('não parece ser um vídeo')) {
        errorMessage += 'O link não contém um vídeo válido.';
      } else if (error.message.includes('corrompido')) {
        errorMessage += 'O arquivo baixado está corrompido.';
      } else if (error.message.includes('galeria')) {
        errorMessage += 'Erro ao salvar na galeria.';
      } else {
        errorMessage += 'Tente novamente.\n\nPossíveis causas:\n• Link inválido ou expirado\n• Vídeo protegido\n• Problemas de conexão';
      }

      Alert.alert('Erro no Download', errorMessage);
    } finally {
      setIsDownloading(false);
      setDownloadProgress(0);
      setDownloadStatus('');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />

      {/* Conteúdo principal baseado na aba ativa */}
      {activeTab === 'browser' && (
        <TabBrowser
          onVideoDetected={handleVideoDetected}
          onDownloadRequest={handleDownloadRequest}
        />
      )}

      {activeTab === 'bookmarks' && (
        <View style={styles.placeholderContainer}>
          <Text style={styles.placeholderText}>Favoritos em desenvolvimento</Text>
        </View>
      )}

      {/* Menu inferior de navegação */}
      <BottomNavigation
        activeTab={activeTab}
        onTabPress={handleTabPress}
        downloadCount={0} // TODO: implementar contagem real
        bookmarkCount={0} // TODO: implementar contagem real
      />

      {/* Modais */}
      <DownloadManager
        visible={showDownloadManager}
        onClose={() => setShowDownloadManager(false)}
      />

      <Settings
        visible={showSettings}
        onClose={() => setShowSettings(false)}
      />

      <VideoQualitySelector
        visible={showQualitySelector}
        onClose={() => setShowQualitySelector(false)}
        videoUrl={currentVideoUrl}
        onQualitySelected={handleQualitySelected}
        onDownloadStart={handleQualitySelected}
      />

      {/* Indicador de download em progresso */}
      {isDownloading && (
        <View style={styles.downloadOverlay}>
          <View style={styles.downloadModal}>
            <Text style={styles.downloadTitle}>Download em Progresso</Text>
            <View style={styles.progressBarContainer}>
              <View
                style={[styles.progressBar, { width: `${downloadProgress}%` }]}
              />
            </View>
            <Text style={styles.downloadStatus}>{downloadStatus}</Text>
            <Text style={styles.downloadProgress}>{downloadProgress}%</Text>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a2e',
  },
  placeholderText: {
    color: '#888',
    fontSize: 16,
  },
  downloadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  downloadModal: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    padding: 30,
    width: width * 0.8,
    alignItems: 'center',
  },
  downloadTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  progressBarContainer: {
    width: '100%',
    height: 8,
    backgroundColor: '#2d2d44',
    borderRadius: 4,
    marginBottom: 15,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#6c5ce7',
    borderRadius: 4,
  },
  downloadStatus: {
    color: '#888',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 10,
  },
  downloadProgress: {
    color: '#6c5ce7',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
