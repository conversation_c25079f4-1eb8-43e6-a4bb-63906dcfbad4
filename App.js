import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  StatusBar,
  SafeAreaView,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import VideoDownloadService from './services/VideoDownloadService';
import UrlValidator from './utils/UrlValidator';
import PermissionManager from './utils/PermissionManager';

const { width, height } = Dimensions.get('window');

export default function App() {
  const [url, setUrl] = useState('');
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [downloadStatus, setDownloadStatus] = useState('');
  const [urlValidation, setUrlValidation] = useState(null);

  useEffect(() => {
    // Limpar arquivos temporários na inicialização
    VideoDownloadService.cleanupTempFiles();
  }, []);

  // Validar URL em tempo real
  useEffect(() => {
    if (url.trim()) {
      const validation = UrlValidator.validateAndGetFeedback(url);
      setUrlValidation(validation);
    } else {
      setUrlValidation(null);
    }
  }, [url]);

  const requestPermissions = async () => {
    const result = await PermissionManager.ensurePermissions();

    if (!result.success) {
      if (!result.cancelled) {
        Alert.alert('Erro', result.message);
      }
      return false;
    }

    return true;
  };

  const downloadVideo = async () => {
    // Validar URL usando o validador avançado
    const validation = UrlValidator.validateAndGetFeedback(url);

    if (!validation.isValid) {
      Alert.alert('Erro', validation.message);
      return;
    }

    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setIsDownloading(true);
    setDownloadProgress(0);
    setDownloadStatus('Analisando URL...');

    try {
      // Callback para progresso do download
      const progressCallback = (downloadProgress) => {
        const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
        setDownloadProgress(Math.round(progress * 100));
        setDownloadStatus('Baixando vídeo...');
      };

      // Usar o serviço de download avançado
      setDownloadStatus('Processando link...');
      const result = await VideoDownloadService.downloadVideo(url, progressCallback);

      setDownloadStatus('Salvando na galeria...');
      await VideoDownloadService.saveToGallery(result.uri, result.fileName);

      Alert.alert(
        'Sucesso!',
        `Vídeo baixado com sucesso!\n\nArquivo: ${result.fileName}\nTamanho: ${(result.size / 1024 / 1024).toFixed(2)} MB\n\nSalvo na galeria do dispositivo.`,
        [{ text: 'OK', onPress: () => setUrl('') }]
      );

    } catch (error) {
      console.error('Erro no download:', error);

      let errorMessage = 'Não foi possível baixar o vídeo. ';

      if (error.message.includes('URL inválida')) {
        errorMessage += 'Verifique se o link está correto.';
      } else if (error.message.includes('não parece ser um vídeo')) {
        errorMessage += 'O link não contém um vídeo válido.';
      } else if (error.message.includes('corrompido')) {
        errorMessage += 'O arquivo baixado está corrompido.';
      } else if (error.message.includes('galeria')) {
        errorMessage += 'Erro ao salvar na galeria.';
      } else {
        errorMessage += 'Tente novamente.\n\nPossíveis causas:\n• Link inválido ou expirado\n• Vídeo protegido\n• Problemas de conexão';
      }

      Alert.alert('Erro no Download', errorMessage);
    } finally {
      setIsDownloading(false);
      setDownloadProgress(0);
      setDownloadStatus('');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      
      {/* Header */}
      <View style={styles.header}>
        <Ionicons name="download" size={32} color="#fff" />
        <Text style={styles.headerTitle}>Video Downloader</Text>
        <Text style={styles.headerSubtitle}>Baixe vídeos de QUALQUER fonte</Text>
      </View>

      {/* Main Content */}
      <View style={styles.content}>
        {/* URL Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Cole o link do vídeo aqui:</Text>
          <View style={styles.inputWrapper}>
            <TextInput
              style={[
                styles.textInput,
                urlValidation && !urlValidation.isValid && styles.textInputError,
                urlValidation && urlValidation.isValid && styles.textInputSuccess
              ]}
              placeholder="https://exemplo.com/video"
              placeholderTextColor="#999"
              value={url}
              onChangeText={setUrl}
              multiline={true}
              numberOfLines={3}
              textAlignVertical="top"
              autoCapitalize="none"
              autoCorrect={false}
            />
            {urlValidation && (
              <View style={styles.validationContainer}>
                <Ionicons
                  name={urlValidation.isValid ? "checkmark-circle" : "alert-circle"}
                  size={16}
                  color={urlValidation.isValid ? "#4CAF50" : "#f44336"}
                />
                <Text style={[
                  styles.validationText,
                  { color: urlValidation.isValid ? "#4CAF50" : "#f44336" }
                ]}>
                  {urlValidation.message}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Progress Indicator */}
        {isDownloading && (
          <View style={styles.progressContainer}>
            <ActivityIndicator size="large" color="#4CAF50" />
            <Text style={styles.progressText}>
              {downloadStatus}
            </Text>
            {downloadProgress > 0 && (
              <>
                <Text style={styles.progressPercentage}>
                  {downloadProgress}%
                </Text>
                <View style={styles.progressBar}>
                  <View
                    style={[styles.progressFill, { width: `${downloadProgress}%` }]}
                  />
                </View>
              </>
            )}
          </View>
        )}

        {/* Instructions */}
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>Como usar:</Text>
          <Text style={styles.instructionsText}>
            1. Cole o link do vídeo no campo acima{'\n'}
            2. Toque no botão de download{'\n'}
            3. Aguarde o download concluir{'\n'}
            4. O vídeo será salvo na sua galeria
          </Text>
        </View>
      </View>

      {/* Download Button */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.downloadButton, isDownloading && styles.downloadButtonDisabled]}
          onPress={downloadVideo}
          disabled={isDownloading}
          activeOpacity={0.8}
        >
          {isDownloading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Ionicons name="arrow-down" size={24} color="#fff" />
          )}
          <Text style={styles.downloadButtonText}>
            {isDownloading ? 'Baixando...' : 'Baixar Vídeo'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  header: {
    backgroundColor: '#1a1a2e',
    paddingVertical: 30,
    paddingHorizontal: 20,
    alignItems: 'center',
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 10,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#ccc',
    marginTop: 5,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  inputContainer: {
    marginBottom: 30,
  },
  inputLabel: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 10,
    fontWeight: '500',
  },
  inputWrapper: {
    position: 'relative',
  },
  textInput: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    padding: 15,
    fontSize: 16,
    color: '#fff',
    borderWidth: 2,
    borderColor: '#333',
    minHeight: 80,
  },
  textInputError: {
    borderColor: '#f44336',
  },
  textInputSuccess: {
    borderColor: '#4CAF50',
  },
  validationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 5,
  },
  validationText: {
    fontSize: 12,
    marginLeft: 5,
    flex: 1,
  },
  progressContainer: {
    alignItems: 'center',
    marginBottom: 30,
    padding: 20,
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
  },
  progressText: {
    color: '#4CAF50',
    fontSize: 16,
    marginTop: 10,
    fontWeight: '500',
  },
  progressPercentage: {
    color: '#fff',
    fontSize: 14,
    marginTop: 5,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: '#333',
    borderRadius: 4,
    marginTop: 10,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  instructionsContainer: {
    backgroundColor: '#1a1a2e',
    padding: 20,
    borderRadius: 15,
    marginBottom: 20,
  },
  instructionsTitle: {
    fontSize: 18,
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  instructionsText: {
    fontSize: 14,
    color: '#ccc',
    lineHeight: 20,
  },
  buttonContainer: {
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  downloadButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    borderRadius: 25,
    shadowColor: '#4CAF50',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
  },
  downloadButtonDisabled: {
    backgroundColor: '#666',
  },
  downloadButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
});
