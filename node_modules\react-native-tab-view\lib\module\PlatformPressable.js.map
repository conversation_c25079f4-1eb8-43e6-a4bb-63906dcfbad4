{"version": 3, "names": ["React", "Platform", "Pressable", "jsx", "_jsx", "ANDROID_VERSION_LOLLIPOP", "ANDROID_SUPPORTS_RIPPLE", "OS", "Version", "PlatformPressable", "disabled", "android_ripple", "pressColor", "pressOpacity", "style", "onPress", "rest", "handlePress", "e", "href", "hasModifierKey", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "isLeftClick", "button", "isSelfTarget", "currentTarget", "undefined", "includes", "target", "preventDefault", "color", "pressed", "cursor", "opacity"], "sourceRoot": "../../src", "sources": ["PlatformPressable.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAEEC,QAAQ,EACRC,SAAS,QAEJ,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAYtB,MAAMC,wBAAwB,GAAG,EAAE;AACnC,MAAMC,uBAAuB,GAC3BL,QAAQ,CAACM,EAAE,KAAK,SAAS,IAAIN,QAAQ,CAACO,OAAO,IAAIH,wBAAwB;;AAE3E;AACA;AACA;AACA,OAAO,SAASI,iBAAiBA,CAAC;EAChCC,QAAQ;EACRC,cAAc;EACdC,UAAU,GAAG,oBAAoB;EACjCC,YAAY;EACZC,KAAK;EACLC,OAAO;EACP,GAAGC;AACE,CAAC,EAAE;EACR,MAAMC,WAAW,GACfC,CAA0E,IACvE;IACH,IAAIjB,QAAQ,CAACM,EAAE,KAAK,KAAK,IAAIS,IAAI,CAACG,IAAI,KAAK,IAAI,EAAE;MAC/C;MACA,MAAMC,cAAc,GACjB,SAAS,IAAIF,CAAC,IAAIA,CAAC,CAACG,OAAO,IAC3B,QAAQ,IAAIH,CAAC,IAAIA,CAAC,CAACI,MAAO,IAC1B,SAAS,IAAIJ,CAAC,IAAIA,CAAC,CAACK,OAAQ,IAC5B,UAAU,IAAIL,CAAC,IAAIA,CAAC,CAACM,QAAS;;MAEjC;MACA,MAAMC,WAAW,GACf,QAAQ,IAAIP,CAAC,GAAGA,CAAC,CAACQ,MAAM,IAAI,IAAI,IAAIR,CAAC,CAACQ,MAAM,KAAK,CAAC,GAAG,IAAI;;MAE3D;MACA,MAAMC,YAAY,GAChBT,CAAC,CAACU,aAAa,IAAI,QAAQ,IAAIV,CAAC,CAACU,aAAa,GAC1C,CAACC,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACZ,CAAC,CAACU,aAAa,CAACG,MAAM,CAAC,GAC9D,IAAI;MAEV,IAAI,CAACX,cAAc,IAAIK,WAAW,IAAIE,YAAY,EAAE;QAClDT,CAAC,CAACc,cAAc,CAAC,CAAC;QAClB;QACA;QACAjB,OAAO,GAAGG,CAAC,CAAC;MACd;IACF,CAAC,MAAM;MACLH,OAAO,GAAGG,CAAC,CAAC;IACd;EACF,CAAC;EAED,oBACEd,IAAA,CAACF,SAAS;IACRS,cAAc,EACZL,uBAAuB,GACnB;MAAE2B,KAAK,EAAErB,UAAU;MAAE,GAAGD;IAAe,CAAC,GACxCkB,SACL;IACDf,KAAK,EAAEA,CAAC;MAAEoB;IAAQ,CAAC,KAAK,CACtB;MACEC,MAAM,EACJlC,QAAQ,CAACM,EAAE,KAAK,KAAK,IAAIN,QAAQ,CAACM,EAAE,KAAK,KAAK;MAC1C;MACA;MACA,SAAS,GACT,MAAM;MACZ6B,OAAO,EAAEF,OAAO,IAAI,CAAC5B,uBAAuB,GAAGO,YAAY,GAAG;IAChE,CAAC,EACD,OAAOC,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC;MAAEoB;IAAQ,CAAC,CAAC,GAAGpB,KAAK,CACxD;IACFC,OAAO,EAAEL,QAAQ,GAAGmB,SAAS,GAAGZ,WAAY;IAAA,GACxCD;EAAI,CACT,CAAC;AAEN", "ignoreList": []}