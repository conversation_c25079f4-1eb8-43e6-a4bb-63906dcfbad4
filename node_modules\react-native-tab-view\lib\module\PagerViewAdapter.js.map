{"version": 3, "names": ["React", "Animated", "Keyboard", "Platform", "StyleSheet", "ViewPager", "useLatestCallback", "useAnimatedValue", "jsx", "_jsx", "AnimatedViewPager", "createAnimatedComponent", "useNativeDriver", "OS", "PagerViewAdapter", "keyboardDismissMode", "swipeEnabled", "navigationState", "onIndexChange", "onTabSelect", "onSwipeStart", "onSwipeEnd", "children", "style", "animationEnabled", "rest", "index", "listenersRef", "useRef", "pagerRef", "indexRef", "navigationStateRef", "position", "offset", "useEffect", "current", "jumpTo", "key", "routes", "findIndex", "route", "setPage", "setPageWithoutAnimation", "setValue", "dismiss", "onPageScrollStateChanged", "state", "pageScrollState", "nativeEvent", "subscription", "addListener", "value", "next", "Math", "ceil", "floor", "for<PERSON>ach", "listener", "removeListener", "addEnterListener", "push", "indexOf", "splice", "memoizedPosition", "useMemo", "add", "render", "ref", "styles", "container", "initialPage", "onPageScroll", "event", "onPageSelected", "e", "scrollEnabled", "create", "flex"], "sourceRoot": "../../src", "sources": ["PagerViewAdapter.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AACvE,OAAOC,SAAS,MAET,yBAAyB;AAChC,OAAOC,iBAAiB,MAAM,qBAAqB;AASnD,SAASC,gBAAgB,QAAQ,uBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEtD,MAAMC,iBAAiB,GAAGT,QAAQ,CAACU,uBAAuB,CAACN,SAAS,CAAC;AAqBrE,MAAMO,eAAe,GAAGT,QAAQ,CAACU,EAAE,KAAK,KAAK;AAE7C,OAAO,SAASC,gBAAgBA,CAAkB;EAChDC,mBAAmB,GAAG,MAAM;EAC5BC,YAAY,GAAG,IAAI;EACnBC,eAAe;EACfC,aAAa;EACbC,WAAW;EACXC,YAAY;EACZC,UAAU;EACVC,QAAQ;EACRC,KAAK;EACLC,gBAAgB;EAChB,GAAGC;AACK,CAAC,EAAE;EACX,MAAM;IAAEC;EAAM,CAAC,GAAGT,eAAe;EAEjC,MAAMU,YAAY,GAAG3B,KAAK,CAAC4B,MAAM,CAAa,EAAE,CAAC;EAEjD,MAAMC,QAAQ,GAAG7B,KAAK,CAAC4B,MAAM,CAAY,IAAI,CAAC;EAC9C,MAAME,QAAQ,GAAG9B,KAAK,CAAC4B,MAAM,CAASF,KAAK,CAAC;EAC5C,MAAMK,kBAAkB,GAAG/B,KAAK,CAAC4B,MAAM,CAACX,eAAe,CAAC;EAExD,MAAMe,QAAQ,GAAGzB,gBAAgB,CAACmB,KAAK,CAAC;EACxC,MAAMO,MAAM,GAAG1B,gBAAgB,CAAC,CAAC,CAAC;EAElCP,KAAK,CAACkC,SAAS,CAAC,MAAM;IACpBH,kBAAkB,CAACI,OAAO,GAAGlB,eAAe;EAC9C,CAAC,CAAC;EAEF,MAAMmB,MAAM,GAAG9B,iBAAiB,CAAE+B,GAAW,IAAK;IAChD,MAAMX,KAAK,GAAGK,kBAAkB,CAACI,OAAO,CAACG,MAAM,CAACC,SAAS,CACtDC,KAAsB,IAAKA,KAAK,CAACH,GAAG,KAAKA,GAC5C,CAAC;IAED,IAAIb,gBAAgB,EAAE;MACpBK,QAAQ,CAACM,OAAO,EAAEM,OAAO,CAACf,KAAK,CAAC;IAClC,CAAC,MAAM;MACLG,QAAQ,CAACM,OAAO,EAAEO,uBAAuB,CAAChB,KAAK,CAAC;MAChDM,QAAQ,CAACW,QAAQ,CAACjB,KAAK,CAAC;IAC1B;IAEAR,aAAa,CAACQ,KAAK,CAAC;EACtB,CAAC,CAAC;EAEF1B,KAAK,CAACkC,SAAS,CAAC,MAAM;IACpB,IAAInB,mBAAmB,KAAK,MAAM,EAAE;MAClCb,QAAQ,CAAC0C,OAAO,CAAC,CAAC;IACpB;IAEA,IAAId,QAAQ,CAACK,OAAO,KAAKT,KAAK,EAAE;MAC9B,IAAIF,gBAAgB,EAAE;QACpBK,QAAQ,CAACM,OAAO,EAAEM,OAAO,CAACf,KAAK,CAAC;MAClC,CAAC,MAAM;QACLG,QAAQ,CAACM,OAAO,EAAEO,uBAAuB,CAAChB,KAAK,CAAC;QAChDM,QAAQ,CAACW,QAAQ,CAACjB,KAAK,CAAC;MAC1B;IACF;EACF,CAAC,EAAE,CAACX,mBAAmB,EAAEW,KAAK,EAAEF,gBAAgB,EAAEQ,QAAQ,CAAC,CAAC;EAE5D,MAAMa,wBAAwB,GAC5BC,KAAwC,IACrC;IACH,MAAM;MAAEC;IAAgB,CAAC,GAAGD,KAAK,CAACE,WAAW;IAE7C,QAAQD,eAAe;MACrB,KAAK,MAAM;QACT1B,UAAU,GAAG,CAAC;QACd;MACF,KAAK,UAAU;QAAE;UACf,MAAM4B,YAAY,GAAGhB,MAAM,CAACiB,WAAW,CAAC,CAAC;YAAEC;UAAM,CAAC,KAAK;YACrD,MAAMC,IAAI,GACR1B,KAAK,IAAIyB,KAAK,GAAG,CAAC,GAAGE,IAAI,CAACC,IAAI,CAACH,KAAK,CAAC,GAAGE,IAAI,CAACE,KAAK,CAACJ,KAAK,CAAC,CAAC;YAE5D,IAAIC,IAAI,KAAK1B,KAAK,EAAE;cAClBC,YAAY,CAACQ,OAAO,CAACqB,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAACL,IAAI,CAAC,CAAC;YAC5D;YAEAnB,MAAM,CAACyB,cAAc,CAACT,YAAY,CAAC;UACrC,CAAC,CAAC;UAEF7B,YAAY,GAAG,CAAC;UAChB;QACF;IACF;EACF,CAAC;EAED,MAAMuC,gBAAgB,GAAGrD,iBAAiB,CAAEmD,QAAkB,IAAK;IACjE9B,YAAY,CAACQ,OAAO,CAACyB,IAAI,CAACH,QAAQ,CAAC;IAEnC,OAAO,MAAM;MACX,MAAM/B,KAAK,GAAGC,YAAY,CAACQ,OAAO,CAAC0B,OAAO,CAACJ,QAAQ,CAAC;MAEpD,IAAI/B,KAAK,GAAG,CAAC,CAAC,EAAE;QACdC,YAAY,CAACQ,OAAO,CAAC2B,MAAM,CAACpC,KAAK,EAAE,CAAC,CAAC;MACvC;IACF,CAAC;EACH,CAAC,CAAC;EAEF,MAAMqC,gBAAgB,GAAG/D,KAAK,CAACgE,OAAO,CACpC,MAAM/D,QAAQ,CAACgE,GAAG,CAACjC,QAAQ,EAAEC,MAAM,CAAC,EACpC,CAACA,MAAM,EAAED,QAAQ,CACnB,CAAC;EAED,OAAOV,QAAQ,CAAC;IACdU,QAAQ,EAAE+B,gBAAgB;IAC1BJ,gBAAgB;IAChBvB,MAAM;IACN8B,MAAM,EAAG5C,QAAQ,iBACfb,IAAA,CAACC,iBAAiB;MAAA,GACZe,IAAI;MACR0C,GAAG,EAAEtC,QAAS;MACdN,KAAK,EAAE,CAAC6C,MAAM,CAACC,SAAS,EAAE9C,KAAK,CAAE;MACjC+C,WAAW,EAAE5C,KAAM;MACnBX,mBAAmB,EACjBA,mBAAmB,KAAK,MAAM,GAAG,SAAS,GAAGA,mBAC9C;MACDwD,YAAY,EAAEtE,QAAQ,CAACuE,KAAK,CAC1B,CACE;QACExB,WAAW,EAAE;UACXhB,QAAQ,EAAEA,QAAQ;UAClBC,MAAM,EAAEA;QACV;MACF,CAAC,CACF,EACD;QAAErB;MAAgB,CACpB,CAAE;MACF6D,cAAc,EAAGC,CAAC,IAAK;QACrB,MAAMhD,KAAK,GAAGgD,CAAC,CAAC1B,WAAW,CAAChB,QAAQ;QACpCF,QAAQ,CAACK,OAAO,GAAGT,KAAK;QACxBR,aAAa,CAACQ,KAAK,CAAC;QACpBP,WAAW,GAAG;UAAEO;QAAM,CAAC,CAAC;MAC1B,CAAE;MACFmB,wBAAwB,EAAEA,wBAAyB;MACnD8B,aAAa,EAAE3D,YAAa;MAAAM,QAAA,EAE3BA;IAAQ,CACQ;EAEvB,CAAC,CAAC;AACJ;AAEA,MAAM8C,MAAM,GAAGhE,UAAU,CAACwE,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}